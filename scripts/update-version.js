// Script: scripts/update-version.js
// Tự động cập nhật version cho app.json và package.json từ version.ts
/* eslint-disable no-console */
/* eslint-disable  @typescript-eslint/no-require-imports*/
const fs = require("fs")
const path = require("path")

// Đọc version từ version.ts
const versionFile = path.join(__dirname, "../version.ts")
const versionContent = fs.readFileSync(versionFile, "utf8")
const major = parseInt(versionContent.match(/MAJOR_VERSION\s*=\s*(\d+)/)?.[1] || "0", 10)
const minor = parseInt(versionContent.match(/MINOR_VERSION\s*=\s*(\d+)/)?.[1] || "0", 10)
const patch = parseInt(versionContent.match(/PATCH_VERSION\s*=\s*(\d+)/)?.[1] || "0", 10)
const version = `${major}.${minor}.${patch}`
const versionCode = major * 10000 + minor * 100 + patch

// Cập nhật app.json
const appJsonPath = path.join(__dirname, "../app.json")
const appJson = JSON.parse(fs.readFileSync(appJsonPath, "utf8"))
if (appJson.expo) {
  if (appJson.expo.version !== version) {
    appJson.expo.version = version
    if (!appJson.expo.android) appJson.expo.android = {}
    appJson.expo.android.versionCode = versionCode
    fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2))
    console.log(`Đã cập nhật app.json version thành ${version}`)
    console.log(`Đã cập nhật app.json android.versionCode thành ${versionCode}`)
  } else {
    if (!appJson.expo.android) appJson.expo.android = {}
    if (appJson.expo.android.versionCode !== versionCode) {
      appJson.expo.android.versionCode = versionCode
      fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2))
      console.log(`Đã đồng bộ app.json android.versionCode thành ${versionCode}`)
    } else {
      console.log("app.json đã đúng version và versionCode.")
    }
  }
} else {
  console.log("Không tìm thấy trường expo trong app.json.")
}

// Cập nhật package.json
const pkgJsonPath = path.join(__dirname, "../package.json")
const pkgJson = JSON.parse(fs.readFileSync(pkgJsonPath, "utf8"))
if (pkgJson.version !== version) {
  pkgJson.version = version
  fs.writeFileSync(pkgJsonPath, JSON.stringify(pkgJson, null, 2))
  console.log(`Đã cập nhật package.json version thành ${version}`)
} else {
  console.log("package.json đã đúng version.")
}
