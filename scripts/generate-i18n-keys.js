/* eslint-disable @typescript-eslint/no-require-imports */

const fs = require("fs")
const path = require("path")
const { glob } = require("glob")

// Only match t("...") or t('...')
const patterns = [
  /t\(["']([^"']+)["']\)/g, // t("key") or t('key')
]

// Function to extract keys from content
function extractKeys(content) {
  const keys = new Set()

  patterns.forEach((pattern) => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      keys.add(match[1])
    }
  })

  return Array.from(keys)
}

// Function to read and parse existing en.json
function readExistingTranslations() {
  try {
    const content = fs.readFileSync(path.join(__dirname, "../src/i18n/locales/en.json"), "utf8")
    return JSON.parse(content)
  } catch {
    return {}
  }
}

// Main function
async function generateI18nKeys() {
  // Get all TypeScript/JavaScript files
  const files = glob.sync("src/**/*.{ts,tsx,js,jsx}")

  const allKeys = new Set()

  // Extract keys from each file
  files.forEach((file) => {
    const content = fs.readFileSync(file, "utf8")
    const keys = extractKeys(content)
    keys.forEach((key) => allKeys.add(key))
  })

  // Read existing translations
  const existingTranslations = readExistingTranslations()

  // Create new translations object by preserving existing translations
  const newTranslations = { ...existingTranslations }

  // Only add missing keys
  let addedKeysCount = 0
  allKeys.forEach((key) => {
    if (!existingTranslations.hasOwnProperty(key)) {
      newTranslations[key] = key
      addedKeysCount++
    }
  })

  // Write to en.json
  /* eslint-disable no-console */
  const outputPath = path.join(__dirname, "../src/i18n/locales/en.json")
  fs.writeFileSync(outputPath, JSON.stringify(newTranslations, null, 2))

  console.log(`Added ${addedKeysCount} new translation keys`)
  console.log(`Total keys: ${Object.keys(newTranslations).length}`)
  console.log(`Output written to ${outputPath}`)
}

generateI18nKeys().catch(console.error)
