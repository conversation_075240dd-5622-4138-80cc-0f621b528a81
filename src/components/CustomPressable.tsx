import React, { ReactNode } from "react"
import { Pressable, StyleProp, View, ViewStyle } from "react-native"

interface CustomPressableProps {
  onPress?: () => void
  children: ReactNode
  enabled?: boolean
  style?: StyleProp<ViewStyle>
  scale?: number
}

export const CustomPressable: React.FC<CustomPressableProps> = ({
  onPress,
  children,
  enabled = true,
  style,
  scale = 1.02,
}) => {
  const renderContent = () => {
    if (!enabled) {
      return <View style={style}>{children}</View>
    }

    return (
      <Pressable
        onPress={onPress}
        style={({ pressed }) => [
          {
            transform: [{ scale: pressed ? scale : 1 }],
          },
          style,
        ]}
      >
        {children}
      </Pressable>
    )
  }

  return renderContent()
}
