import React from "react"
import { StyleSheet, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import { TextInput } from "react-native-gesture-handler"
import Colors from "src/config/colors"
import { LabelView } from "./LabelView"
import { ErrorLabel } from "./ErrorLabel"

interface InputFieldProps {
  label?: string
  value: string | number // Value can be string or number
  onChangeText: (text: string | number) => void
  placeholder?: string
  multiline?: boolean
  height?: number
  require?: boolean
  style?: ViewStyle
  onBlur?: () => void
  error?: string
  type?: "string" | "number"
  disabled?: boolean
  trailingIcon?: React.ReactNode
}

const InputField: React.FC<InputFieldProps> = ({
  label,
  value,
  onChangeText,
  require,
  placeholder,
  multiline = false,
  height = 38,
  style,
  onBlur,
  error,
  type = "string",
  disabled = false,
  trailingIcon,
}) => {
  const handleChangeText = (text: string) => {
    const trimmedText = text.trimStart()

    if (type === "number") {
      // Allow digits, dot (.), comma (,), and minus sign (-) initially
      let processedText = trimmedText.replace(/[^0-9.,-]/g, "")

      // Replace all commas with dots for internal representation
      processedText = processedText.replace(/,/g, ".")

      // Ensure only one decimal point exists
      const parts = processedText.split(".")
      if (parts.length > 2) {
        // If more than one dot, keep the part before the first dot and append the rest without dots
        processedText = parts[0] + "." + parts.slice(1).join("")
      }

      // Ensure minus sign is only at the beginning
      if (processedText.indexOf("-") > 0) {
        processedText = processedText.replace(/-/g, "") // Remove any '-' not at the start
      }
      // Handle multiple leading minus signs (e.g., "--123" becomes "-")
      if (processedText.startsWith("--")) {
        processedText = "-"
      }

      // Only call onChangeText if the result is a valid number string representation
      // or a valid intermediate state like "", "-" or "1."
      if (processedText === "" || processedText === "-") {
        onChangeText(processedText)
      } else if (!isNaN(parseFloat(processedText))) {
        // Check if it's a number or a number followed by a dot (e.g., "1.", "12.")
        // This regex allows numbers like "1", "1.", "1.2", "-1", "-1.", "-1.2"
        if (/^-?\d*\.?\d*$/.test(processedText)) {
          onChangeText(processedText)
        }
      }
      // If none of the above, do not call onChangeText, effectively ignoring invalid input
      // This prevents inputs like "1.2.3" or "abc" from updating the value state.
    } else if (type === "string") {
      onChangeText(trimmedText)
    }
  }

  // Use inputMode="decimal" for numbers to bring up appropriate keyboard
  const inputMode = type === "number" ? "decimal" : "text"

  return (
    <View style={style}>
      {label && <LabelView label={label} require={require} style={styles.label} />}
      <View style={styles.inputContainer}>
        <TextInput
          onBlur={onBlur}
          // Ensure value is always a string for TextInput
          value={value.toString()}
          onChangeText={handleChangeText}
          placeholder={placeholder}
          placeholderTextColor={Colors.Neutral700}
          multiline={multiline}
          inputMode={inputMode} // Use inputMode
          keyboardType={type === "number" ? "decimal-pad" : "default"} // Use decimal-pad for better number input experience
          style={[styles.input, { height }, disabled && styles.disabledInput]}
          editable={!disabled}
          // Add accessibilityLabel if needed
          // accessibilityLabel={label ? label : placeholder}
        />
        {trailingIcon && <View style={styles.iconContainer}>{trailingIcon}</View>}
      </View>
      <ErrorLabel error={error} />
    </View>
  )
}

const styles = StyleSheet.create({
  label: {
    marginBottom: 4,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingHorizontal: 12,
  },
  input: {
    flex: 1,
    height: 38,
    ...textStyles.MMedium,
    color: Colors.PalleteWhite, // Assuming PalleteWhite is a valid color
    paddingVertical: 0, // Adjust vertical padding if needed
  },
  iconContainer: {
    // Position the icon absolutely to the right within the inputContainer
    position: "absolute",
    right: 12,
    // Center vertically
    top: 0,
    bottom: 0,
    justifyContent: "center",
  },
  disabledInput: {
    backgroundColor: Colors.Neutral900, // Assuming Neutral900 is a valid color
    color: Colors.Neutral300, // Assuming Neutral300 is a valid color
  },
})

export { InputField }
