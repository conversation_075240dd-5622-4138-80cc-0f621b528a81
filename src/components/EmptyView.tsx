import Colors from "src/config/colors"
import React from "react"
import { Image, StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import iconFolderX from "assets/images/ic_folder_x.png"
interface EmptyViewProps {
  style?: ViewStyle
  title?: string
  subtitle?: string
  imageSource?: any
  imageSize?: number
}

const EmptyView: React.FC<EmptyViewProps> = ({ title, subtitle }) => {
  return (
    <View style={[styles.folderXParent, styles.parentFlexBox]}>
      <Image width={40} height={40} source={iconFolderX} />
      <View style={[styles.noDisbursedLoansHaveBeenSParent, styles.parentFlexBox]}>
        <Text style={[styles.noDisbursedLoans, styles.waitForLendersTypo]}>{title}</Text>
        <Text style={[styles.waitForLenders, styles.waitForLendersTypo]}>{subtitle} </Text>
      </View>
    </View>
  )
}

export { EmptyView }

const styles = StyleSheet.create({
  parentFlexBox: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  folderXParent: {
    flex: 1,
    backgroundColor: Colors.blackNew,
    paddingHorizontal: 24,
    gap: 24,
    paddingVertical: 56,
  },
  noDisbursedLoansHaveBeenSParent: {
    gap: 8,
    width: "100%",
  },
  waitForLendersTypo: {
    textAlign: "center",
  },
  noDisbursedLoans: {
    ...textStyles.LMedium,
    color: Colors.PalleteWhite,
  },
  waitForLenders: {
    ...textStyles.MRegular,
    color: Colors.Neutral400,
    lineHeight: 18,
  },
})
