import React from "react"
import { Image, StyleSheet, Text, View, ViewStyle } from "react-native"
import { CustomPressable } from "./CustomPressable"
import { textStyles, viewStyles } from "src/config/styles"
import { NavigationProp, ParamListBase, useNavigation } from "@react-navigation/native"
import Colors from "@/config/colors"
import icBack from "assets/images/ic_chevron_left.png"
import { Divider } from "react-native-paper"

interface TopBarProps {
  rightIcon?: React.ReactNode
  enableBack?: boolean
  style?: ViewStyle
  title: string
}

const TopBar: React.FC<TopBarProps> = ({ enableBack, title, style }) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const onGoBack = () => {
    navigation.goBack()
  }

  return (
    <>
      <View style={style ? style : styles.container}>
        {enableBack && (
          <CustomPressable onPress={onGoBack}>
            <Image source={icBack} style={viewStyles.size18Icon} />
          </CustomPressable>
        )}
        <Text style={styles.title} numberOfLines={1}>
          {title}
        </Text>
      </View>
      <Divider style={styles.divider} />
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    backgroundColor: Colors.PalleteBlack,
    paddingVertical: 20,
    paddingHorizontal: 16,
    gap: 10,
  },

  title: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  divider: {
    height: 1,
    width: "100%",
    backgroundColor: Colors.Neutral900,
  },
})

export { TopBar }
