import Colors from "@/config/colors"
import { textStyles } from "@/config/styles"
import useLoadingStore from "@/stores/loadingStore"
import React from "react"
import { useTranslation } from "react-i18next"
import { View, ActivityIndicator, StyleSheet, Text, Modal } from "react-native"
import { PrimaryButton } from "./Button"

const Loading = () => {
  const { t } = useTranslation()
  const { setLoading, showCancelBtn } = useLoadingStore()
  return (
    <Modal visible={true} transparent={true} style={styles.container}>
      <View style={styles.container}>
        <ActivityIndicator size="large" color={Colors.Primary500} />
        <Text style={textStyles.LRegular}>{t("Loading...")}</Text>
        {showCancelBtn && (
          <PrimaryButton
            title={t("Cancel")}
            onPress={() => {
              setLoading(false)
            }}
          />
        )}
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    backgroundColor: Colors.PalleteBlack80,
  },
})

export default Loading
