import React from "react"
import { StyleProp, StyleSheet, View, ViewStyle } from "react-native"
import Colors from "src/config/colors"

interface CardViewProps {
  children: React.ReactNode
  style?: StyleProp<ViewStyle>
  borderRadius?: number
  shadowColor?: string
  shadowOffset?: {
    width: number
    height: number
  }
  shadowOpacity?: number
  shadowRadius?: number
  elevation?: number
}

export const CardView: React.FC<CardViewProps> = ({ children, style }) => {
  return <View style={[styles.card, style]}>{children}</View>
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.Neutral950,
    borderRadius: 6,
  },
})
