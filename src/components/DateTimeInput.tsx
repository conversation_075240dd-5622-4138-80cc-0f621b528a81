import React, { useCallback, useState } from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import RNDateTimePicker, { DateTimePickerEvent } from "@react-native-community/datetimepicker"
import { CustomPressable } from "./CustomPressable"
import { LabelView } from "./LabelView"
import icCalendar from "assets/images/ic_calendar.png"

interface DateTimeInputProps {
  title: string
  value: string
  require?: boolean
  disabled?: boolean
  onChangeDate: (selectedDate?: Date) => void
  placeholder?: string
  minimumDate?: Date
}

const DateTimeInput: React.FC<DateTimeInputProps> = ({
  title,
  value,
  require,
  onChangeDate,
  placeholder = "dd/mm/yyyy",
  disabled = false,
  minimumDate,
}) => {
  const [showDatePicker, setShowDatePicker] = useState(false)

  const handlePress = useCallback(() => {
    setShowDatePicker(true)
  }, [])

  const handleDateChange = useCallback(
    (event: DateTimePickerEvent, selectedDate?: Date) => {
      const currentDate = selectedDate || new Date()
      onChangeDate(currentDate)
      setShowDatePicker(false)
    },
    [onChangeDate],
  )

  return (
    <View style={styles.container}>
      <LabelView label={title} require={require} style={styles.marginBottom4} />
      <CustomPressable onPress={handlePress} enabled={!disabled}>
        <View
          style={[
            styles.input,
            {
              backgroundColor: disabled ? Colors.Neutral900 : Colors.PalleteBlack,
            },
          ]}
        >
          <Text
            style={[
              textStyles.MMedium,
              { color: disabled ? Colors.Neutral300 : Colors.PalleteWhite },
            ]}
          >
            {value || placeholder}
          </Text>
          <Image source={icCalendar} style={viewStyles.size16Icon} />
        </View>
      </CustomPressable>

      {showDatePicker && (
        <RNDateTimePicker
          value={value ? new Date(value) : new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={minimumDate}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    width: "100%",
  },
  marginBottom4: {
    marginBottom: 4,
  },
  label: {
    marginBottom: 8,
  },
  cardContainer: {
    borderRadius: 8,
    shadowOpacity: 0.1,
    elevation: 2,
  },
  input: {
    width: "100%",
    flexDirection: "row",
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: Colors.Neutral900,
    justifyContent: "space-between",
    alignItems: "center",
  },
})

export { DateTimeInput }
