import React from "react"
import { Image, StyleSheet, View } from "react-native"
import { PrimaryButton } from "./Button"
import { CustomPressable } from "./CustomPressable"
import Colors from "src/config/colors"
import logoIcon from "assets/images/ic_logo.png"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import icPlus from "assets/images/ic_plus.png"
import * as Routes from "src/navigation/Routers"
import useAuthStore from "@/stores/authStore"
import { useAppNavigaton } from "src/navigation/Routers"

export const MainTabHeader: React.FC = () => {
  const { t } = useTranslation()
  const { isAuthenticated } = useAuthStore()
  const navigation = useAppNavigaton()

  const handleTokenize = () => {
    navigation.navigate(Routes.CREATE_LOAN)
  }

  return (
    <View style={styles.container}>
      <CustomPressable>
        <Image source={logoIcon} style={styles.logo} />
      </CustomPressable>

      {isAuthenticated && (
        <PrimaryButton
          title={t("Create Loan")}
          onPress={handleTokenize}
          height={28}
          borderRadius={4}
          style={styles.tokenizeButton}
          icon={
            <Image source={icPlus} style={viewStyles.size12Icon} tintColor={Colors.PalleteBlack} />
          }
          textStyle={textStyles.SMedium}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    height: 48,
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: Colors.blackNew,
    paddingHorizontal: 16,
  },
  logo: {
    width: 15,
    height: 20,
  },
  tokenizeButton: {
    marginRight: 4,
  },
})
