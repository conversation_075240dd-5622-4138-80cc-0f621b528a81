{"welcome": "Welcome", "greeting": "Hello, how are you?", "go_to_secondary": "Go to Secondary Screen", "selectNFTsToLend": "Select NFTs to Lend", "principal": "Principal", "apr": "APR", "duration": "Duration", "nftCollateral": "NFT Collateral", "totalRepayment": "Total Repayment", "selectedRowsPlaceholder": "{{count}} of {{total}} selected", "pageInfoPlaceholder": "{{current}} of {{total}} pages", "prevPage": "Prev", "nextPage": "Next", "quantityOwned": "Quantity Owned: {{count}} NFTs", "\\n": "\\n", ":": ":", "Sunday": "Sunday", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "year": "year", "years": "years", "day": "day", "days": "days", "hour": "hour", "hours": "hours", "minute": "minute", "minutes": "minutes", "just now": "just now", "second": "second", "seconds": "seconds", "-": "-", "ago": "ago", "window": "window", ".": ".", "/": "/", "Take Photo": "Take Photo", "Choose from Library": "Choose from Library", "Cancel": "Cancel", "Request permission": "Request permission", "You need to grant camera access": "You need to grant camera access", "Open settings": "Open settings", "You need to grant access to the photo library": "You need to grant access to the photo library", "Could not bind Multipart Form": "Could not bind Multipart Form", "Invalid request": "Invalid request", "Invalid nonce": "Invalid nonce", "Invalid signature": "Invalid signature", "Invalid file": "Invalid file", "Could not parse Multipart Form": "Could not parse Multipart Form", "Could not get file from Multipart Form": "Could not get file from Multipart Form", "Could not get Multipart Form data": "Could not get Multipart Form data", "At least 2 Credential photos required": "At least 2 Credential photos required", "At least 4 Estate photos required": "At least 4 Estate photos required", "Unsupported zone": "Unsupported zone", "Could not bind Pagination": "Could not bind Pagination", "Invalid path parameter": "Invalid path parameter", "Wrong broker email verification code": "Wrong broker email verification code", "Could not bind Filter Query": "Could not bind Filter Query", "Invalid query parameter": "Invalid query parameter", "Unavailable currency": "Unavailable currency", "Invalid unit price": "Invalid unit price", "Broker email has already been verified": "Broker email has already been verified", "Broker email verification code expired": "Broker email verification code expired", "Invalid authorization header": "Invalid authorization header", "Invalid JWT": "Invalid JWT", "Malformed JWT": "Malformed JWT", "Wrong password": "Wrong password", "Access denied": "Access denied", "User has already been verified": "User has already been verified", "Nonce does not exist": "<PERSON><PERSON> does not exist", "Currency not found": "Currency not found", "Broker wallet not found": "Broker wallet not found", "Estate token metadata not found": "Estate token metadata not found", "Tokenization application not found": "Tokenization application not found", "Broker email not found": "Broker email not found", "Internal error": "Internal error", "Could not generate nonce": "Could not generate nonce", "Could not set nonce to Redis": "Could not set nonce to <PERSON>is", "Could not get nonce from Redis": "Could not get nonce from Redis", "Could not generate JWT string": "Could not generate JWT string", "Could not get my address from Gin Context": "Could not get my address from Gin Context", "My address from Gin Context is invalid": "My address from Gin Context is invalid", "Could not get or create user": "Could not get or create user", "Could not create estate token metadata": "Could not create estate token metadata", "Could not create tokenization application": "Could not create tokenization application", "Could not upload tokenization application files": "Could not upload tokenization application files", "Could not create tokenization application files record": "Could not create tokenization application files record", "Database error": "Database error", "Could not get file from S3": "Could not get file from S3", "Could not pin file to Pinata": "Could not pin file to <PERSON><PERSON><PERSON>", "Could not marshal EstateTokenMetadata JSON": "Could not marshal EstateTokenMetadata JSON", "Could not encrypt broker password": "Could not encrypt broker password", "Could not send email via Sendgrid": "Could not send email via Sendgrid", "Email verification code expiredAt is null": "Email verification code expiredAt is null", "Could not get broker ID from Gin Context": "Could not get broker ID from Gin Context", "Invalid broker ID from Gin Context": "Invalid broker ID from Gin Context", "Could not open national ID card image file": "Could not open national ID card image file", "Could not upload national ID card image file": "Could not upload national ID card image file", "Could not create file record": "Could not create file record", "Could not override user KYC data": "Could not override user KYC data", "Could not open avatar image file": "Could not open avatar image file", "Could not upload avatar image file": "Could not upload avatar image file", "Could not override user data": "Could not override user data", "Could not find estate": "Could not find estate", "Could not create appraisal document files": "Could not create appraisal document files", "Could not create land registry document files": "Could not create land registry document files", "An error has occurred!": "An error has occurred!", "An error has occurred": "An error has occurred", "Address copied": "Address copied", "Nationality": "Nationality", "Date of Birth": "Date of Birth", "Address": "Address", "Email": "Email", "Phone": "Phone", "Account Verified": "Account Verified", "Verify my account": "Verify my account", "Account Verifying": "Account Verifying", "Edit Profile": "Edit Profile", "Share": "Share", "Disconnect": "Disconnect", "Please connect your wallet to view your profile details.": "Please connect your wallet to view your profile details.", "Connect Wallet": "Connect Wallet", "Create Loan": "Create Loan", "Preview": "Preview", "Loan Amount": "<PERSON><PERSON>", "APR": "APR", "%": "%", "Duration": "Duration", "NFT Collateral": "NFT Collateral", "NFTs": "NFTs", "Total Repayment": "Total Repayment", "No NFT Selected": "No NFT Selected", "Lend": "Lend", "Principal": "Principal", "Repayment": "Repayment", "Select NFTs to Lend": "Select NFTs to Lend", "Home": "Home", "Explore": "Explore", "Profile": "Profile", "Settings": "Settings", "Loading...": "Loading...", "Tokenize": "Tokenize", "Oops!": "Oops!", "Display name": "Display name", "Date of birth": "Date of birth", "Nationality ID": "Nationality ID", "Front of ID Card": "Front of ID Card", "Back of ID Card": "Back of ID Card", "Submit": "Submit", "Full name must be at least 2 characters": "Full name must be at least 2 characters", "Full name must be at most 100 characters": "Full name must be at most 100 characters", "Please select Date of birth": "Please select Date of birth", "You must be at least 18 years old": "You must be at least 18 years old", "Citizen ID must be at least 9 characters": "Citizen ID must be at least 9 characters", "Citizen ID must be at most 12 characters": "Citizen ID must be at most 12 characters", "Verify account success": "Verify account success", "Verify account fail": "Verify account fail", "Upload file": "Upload file", "Edit profile": "Edit profile", "Wallet": "Wallet", "Avatar": "Avatar", "Save": "Save", "Phone number must be at least 10 digits": "Phone number must be at least 10 digits", "Phone number must not exceed 12 digits": "Phone number must not exceed 12 digits", "Edit profile success": "Edit profile success", "Edit profile fail": "Edit profile fail", "Featured": "Featured", "High-Interest": "High-Interest", "Open Loan Listings": "Open Loan Listings", "Loan Sales Marketplace": "Loan Sales Marketplace", "Repaid Loans": "Repaid <PERSON>", "Language": "Language", "Informations": "Informations", "References": "References", "Contact": "Contact", "Offices": "Offices", "Other": "Other", "Delete account": "Delete account", "Viet Nam Office": "Viet Nam Office", "Briky Land Real Estate Trading Floor Joint Stock Company": "Briky Land Real Estate Trading Floor Joint Stock Company", "Tax registration office": "Tax registration office", "No. 1A, Lane 57 Nguyen Khanh Toan, Dich Vong Ward, Cau Giay District, Hanoi City.": "No. 1A, Lane 57 <PERSON><PERSON><PERSON>, <PERSON><PERSON> Ward, Cau Giay District, Hanoi City.", "Hanoi Office 1 - real estate floor headquarters": "Hanoi Office 1 - real estate floor headquarters", "Building N01-T2, Diplomatic Corps Urban Area, Hoang Minh Thao Street, Xuan Tao Ward, Bac Tu Liem District, Hanoi.": "Building N01-T2, Diplomatic Corps Urban Area, Hoang Minh Thao Street, Xuan Tao Ward, Bac Tu Liem District, Hanoi.", "Hanoi Office 2": "Hanoi Office 2", "5th Floor, SME Royal Building, To Hieu Street, Quang Trung Ward, Ha Dong District, Hanoi.": "5th Floor, SME Royal Building, To Hieu Street, Quang Trung Ward, Ha Dong District, Hanoi.", "Ho Chi Minh Office": "Ho Chi Minh Office", "178-180 Le Hong Phong Street, Ward 3, District 5, Ho Chi Minh City.": "178-180 Le Hong Phong Street, Ward 3, District 5, Ho Chi Minh City.", "Singapore Office": "Singapore Office", "Brikyland Holding Pte.Ltd": "Brikyland Holding Pte.Ltd", "114 Lavender Street, #11-83 CT HUB 2, Singapore.": "114 Lavender Street, #11-83 CT HUB 2, Singapore.", "Australia Office": "Australia Office", "Brikyland Australia Pty.Ltd": "Brikyland Australia Pty.Ltd", "Suit 886, 100 George Street, Parramatta NSW 2150 Australia.": "Suit 886, 100 George Street, Parramatta NSW 2150 Australia.", "Dubai Office": "Dubai Office", "Briky Land Virtual Assets Management Investment Services L.L.C": "Briky Land Virtual Assets Management Investment Services L.L.C", "Documentation": "Documentation", "White paper": "White paper", "Vertichain Security Report": "Vertichain Security Report", "VIETNAM": "VIETNAM", "Due date": "Due date", "Details": "Details", "Invalid email": "Invalid email", "Borrow & Create Loan": "Borrow & Create Loan", "My Profile": "My Profile", "Failed to approve": "Failed to approve", "Repay success": "Repay success", "Data will be updated in few seconds": "Data will be updated in few seconds", "Repay failed": "Repay failed", "Repay": "<PERSON>ay", "Lend success": "Lend success", "Lend failed": "Lend failed", "Lend Now": "Lend Now", "Collateral": "Collateral", "Foreclose success": "Foreclose success", "Foreclose failed": "Foreclose failed", "Foreclose": "Foreclose", "Cancel success": "Cancel success", "Cancel failed": "Cancel failed", "X": "X", "Provider not available": "Provider not available", "Failed to set approval for all": "Failed to set approval for all", "Borrow success": "Borrow success", "Borrow failed": "Borrow failed", "Failed to load loan details": "Failed to load loan details", "No loan details found": "No loan details found", "Detail": "Detail", "Posted on": "Posted on", "By": "By", "Lender": "<PERSON><PERSON>", "Selling Price": "Selling <PERSON>", "Listing success": "Listing success", "Listing failed": "Listing failed", "Sell Loan": "<PERSON><PERSON>", "Repaid": "Repaid", "Cancellation success": "Cancellation success", "Cancellation failed": "Cancellation failed", "Purchase success": "Purchase success", "Purchase failed": "Purchase failed", "Buy Loan": "Buy Loan", "My Loan": "My Loan", "Disbursed Loans": "Disbursed Loans", "Loan Sale": "Loan <PERSON>", "No Loans Found": "No Loans Found", "Wait for lenders to submit offers or share your deal to attract them.": "Wait for lenders to submit offers or share your deal to attract them.", "No disbursed loans have been submitted": "No disbursed loans have been submitted", "Status": "Status", "No loan offers have been submitted": "No loan offers have been submitted", "Open": "Open", "Disbursed": "Disbursed", "Buy": "Buy", "Borrower": "<PERSON><PERSON><PERSON>", "Lent": "<PERSON><PERSON>", "Ovedue": "<PERSON><PERSON><PERSON>", "Foreclosed": "Foreclosed", "Canceled": "Canceled", "Unknown status": "Unknown status", "Claim NFT": "Claim {{amount}} Collateral NFT", "Sell price": "Sell price", "Protocal fee": "Protocal fee", "Total Fee": "Total Fee", "Sell": "<PERSON>ll", "Please input a valid sell price greater than 0": "Please input a valid sell price greater than 0", "Unknown Chain": "Unknown Chain", "View More Details": "View More Details", "PM": "PM", "AM": "AM", "Binance Smart Chain": "Binance Smart Chain", "Binance Smart Chain Testnet": "Binance Smart Chain Testnet", "Network": "Network", "Tolkens": "<PERSON><PERSON><PERSON>", "USDT": "USDT", "Failed to load loan sale": "Failed to load loan sale", "No loan sale found": "No loan sale found", "Failed to load home data": "Failed to load home data", "No home data": "No home data", "Leverage Every Property": "Leverage Every Property", "Total Volume": "Total Volume", "Open Loans": "Open Loans", "Completed Loans": "Completed Loans", "Repayment Loan": "Repayment Loan", "Failed to load loans": "Failed to load loans", "No loans found": "No loans found", "All Loans": "All Loans", "Lend now": "Lend now"}