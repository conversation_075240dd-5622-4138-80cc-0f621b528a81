import { BASE_API_URL } from "@/config/env"
import axiosInstance from "./axiosInstance"
import { BaseResponse, ListResponse, SignInResponse, User } from "./types"
import { storeAuthTokens } from "@/utils/storage"
import * as types from "@/service/types"
import { PublicEstate } from "@/service/types"

// TODO: update for lend
export const getNonceApi = async (address: string): Promise<string> => {
  const formData = new FormData()
  formData.append("address", address)

  const res = await axiosInstance.post<{ data: { nonce: string } }>(
    `${BASE_API_URL}/briky/api/auth/nonce`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    },
  )

  return res.data.data.nonce
}

// TODO: update for lend
export const loginApi = async (
  address: string,
  nonce: string,
  signature: string,
): Promise<SignInResponse | null> => {
  const formData = new FormData()
  formData.append("address", address)
  formData.append("nonce", nonce)
  formData.append("signature", signature)

  const res = await axiosInstance.post<BaseResponse<SignInResponse>>(
    `${BASE_API_URL}/briky/api/auth/sign-in`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    },
  )
  if (res.data.data) {
    const { token, refreshToken } = res.data.data
    // Store tokens in AsyncStorage return true;
    await storeAuthTokens(token, refreshToken, address)
    return res.data.data
  }
  return null
}

// TODO: update for lend
export const refreshTokenApi = async (refreshToken: string): Promise<boolean> => {
  const formData = new FormData()
  formData.append("refreshToken", refreshToken)
  const res = await axiosInstance.post<BaseResponse<SignInResponse>>(
    `${BASE_API_URL}/briky/api/auth/refresh`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    },
  )
  if (res.data.data) {
    const { token } = res.data.data
    // Store tokens in AsyncStorage return true;
    await storeAuthTokens(token)
    return true
  }
  return false
}

// TODO: update for lend
export const getProfileApi = async (address?: string): Promise<User> => {
  const response = await axiosInstance.get<{ data: User }>(
    `${BASE_API_URL}/briky/api/users/${address}`,
  )
  return response.data.data
}

// TODO: update for lend
export const verifyProfileApi = async (formData: FormData): Promise<string> => {
  const res = await axiosInstance.patch<{ data?: string }>(
    `${BASE_API_URL}/briky/api/users/me/kyc-data`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    },
  )
  return res.data?.data ?? ""
}

// TODO: update for lend
export const updateProfileApi = async (payload: FormData) => {
  return axiosInstance.put<{ data?: string }>(`${BASE_API_URL}/briky/api/users/me`, payload, {
    headers: {
      "content-type": "multipart/form-data",
    },
  })
}

// TODO: update for lend
export const getEstatesSearchApi = async (
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  },
): Promise<ListResponse<PublicEstate>> => {
  const res = await axiosInstance.get(`${BASE_API_URL}/briky/api/estates/search`, {
    params,
  })
  return res.data?.data
}

export const getCurrencies = async (): Promise<types.Currency[]> => {
  const res = await axiosInstance.get(`${BASE_API_URL}/briky/api/currencies`)
  return res.data.data
}

export const getHomeHighInterestLoansApi = async (): Promise<types.MortgageLoanBE[]> => {
  const res = await axiosInstance.get<{
    data: types.MortgageLoanBE[]
  }>(`${BASE_API_URL}/briky/api/loans/high-interest`)
  return res.data.data || []
}

export const getListLoansApi = async (
  params: { itemsPerPage?: number; currentPage?: number; states?: string; estateId?: string } = {
    itemsPerPage: 10,
    currentPage: 1,
  },
): Promise<ListResponse<types.MortgageLoanBE>> => {
  const res = await axiosInstance.get(`${BASE_API_URL}/briky/api/loans`, { params })
  return res.data?.data
}

export const getLoanDetailByIdApi = async (id: string): Promise<types.MortgageLoanBE> => {
  const res = await axiosInstance.get(`${BASE_API_URL}/briky/api/loans/${id}`)
  return res.data?.data
}

export const getListLoanSaleApi = async (
  params: { itemsPerPage?: number; currentPage?: number; states?: string } = {
    itemsPerPage: 10,
    currentPage: 1,
  },
): Promise<ListResponse<types.OfferBE>> => {
  const res = await axiosInstance.get(`${BASE_API_URL}/briky/api/loans/offers`, { params })
  return res.data?.data
}

export const getListOfferSaleApi = async (
  params: { itemsPerPage?: number; currentPage?: number; states?: string } = {
    itemsPerPage: 10,
    currentPage: 1,
  },
): Promise<ListResponse<types.OfferSaleBE>> => {
  const res = await axiosInstance.get(`${BASE_API_URL}/briky/api/loans/offer-sales`, { params })
  return res.data?.data
}

export const getTotalVolumeApi = async (): Promise<types.TotalVolume[]> => {
  const res = await axiosInstance.get(`${BASE_API_URL}/briky/api/loans/total-repaid-volume`)
  return res.data?.data || []
}

export const getEstatesByIdsApi = async (ids: string[]): Promise<types.Estate[]> => {
  const res = await axiosInstance.get(`${BASE_API_URL}/briky/api/estates`, {
    params: { ids },
  })
  return res.data?.data || []
}

export const getEstateByIdApi = async (id: string): Promise<types.Estate | undefined> => {
  const res = await axiosInstance.get(`${BASE_API_URL}/briky/api/estates/${id}`)
  return res.data?.data
}
