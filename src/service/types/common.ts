export type WalletAddress = `0x${string}` | undefined

export type SignInResponse = {
  token: string
  tokenExpiredTimeInSeconds: number
  refreshToken?: string
  refreshTokenExpiredTimeInSeconds?: number
}

// Base response type for API responses
export type BaseResponse<T = any> = {
  success: boolean
  code: number
  message?: string
  data?: T
  error?: string
}

// Base list response type for paginated data
export type BaseListResponse<T = any> = {
  success: boolean
  code: number
  message?: string
  data?: {
    list: T[]
  }
  error?: string
}

export type Pagination = {
  itemsPerPage: number
  currentPage: number
  totalItems: number
}

export type ListResponse<T> = {
  list: T[]
  pagination: Pagination
}
