export type PublicUser = {
  address: string
  alias: string
  avatarUrl: string
  isVerified: boolean
  name: string
  nationality: string
}

export type User = {
  id: string
  address: string
  email: string
  phone: string
  avatarUrl?: string
  isBroker: boolean
  isModerator: boolean
  isManager: boolean
  isVerified: boolean
  nationality: string
  dob: number
  alias: string
  status: string
}
