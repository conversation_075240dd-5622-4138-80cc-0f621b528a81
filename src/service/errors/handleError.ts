import { useTranslation } from "react-i18next"
import * as Errors from "./type"
import { showError } from "@/utils/toast"

export const useHandleError = () => {
  const { t } = useTranslation()

  const errorMessages: Record<string, string> = {
    // ----------------------- 400_xxx -----------------------
    "400001": t("Could not bind Multipart Form") + " (400001)",
    "400002": t("Invalid request") + " (400002)",
    "400003": t("Invalid nonce") + " (400003)",
    "400004": t("Invalid signature") + " (400004)",
    "400005": t("Invalid file") + " (400005)",
    "400006": t("Could not parse Multipart Form") + " (400006)",
    "400007": t("Could not get file from Multipart Form") + " (400007)",
    "400008": t("Could not get Multipart Form data") + " (400008)",
    "400009": t("At least 2 Credential photos required") + " (400009)",
    "400010": t("At least 4 Estate photos required") + " (400010)",
    "400011": t("Unsupported zone") + " (400011)",
    "400012": t("Could not bind Pagination") + " (400012)",
    "400013": t("Invalid path parameter") + " (400013)",
    "400014": t("Wrong broker email verification code") + " (400014)",
    "400015": t("Could not bind Filter Query") + " (400015)",
    "400016": t("Invalid query parameter") + " (400016)",
    "400017": t("Unavailable currency") + " (400017)",
    "400018": t("Invalid unit price") + " (400018)",
    "400019": t("Broker email has already been verified") + " (400019)",
    "400020": t("Broker email verification code expired") + " (400020)",

    // ----------------------- 401_xxx -----------------------
    "401001": t("Invalid authorization header") + " (401001)",
    "401002": t("Invalid JWT") + " (401002)",
    "401003": t("Malformed JWT") + " (401003)",
    "401004": t("Wrong password") + " (401004)",

    // ----------------------- 403_xxx -----------------------
    "403001": t("Access denied") + " (403001)",
    "403002": t("User has already been verified") + " (403002)",

    // ----------------------- 404_xxx -----------------------
    "404001": t("Nonce does not exist") + " (404001)",
    "404002": t("Currency not found") + " (404002)",
    "404003": t("Broker wallet not found") + " (404003)",
    "404004": t("Estate token metadata not found") + " (404004)",
    "404005": t("Tokenization application not found") + " (404005)",
    "404006": t("Broker email not found") + " (404006)",

    // ----------------------- 500_xxx -----------------------
    "500000": t("Internal error") + " (500000)",
    "500001": t("Could not generate nonce") + " (500001)",
    "500002": t("Could not set nonce to Redis") + " (500002)",
    "500003": t("Could not get nonce from Redis") + " (500003)",
    "500004": t("Could not generate JWT string") + " (500004)",
    "500005": t("Could not get my address from Gin Context") + " (500005)",
    "500006": t("My address from Gin Context is invalid") + " (500006)",
    "500007": t("Could not get or create user") + " (500007)",
    "500008": t("Could not create estate token metadata") + " (500008)",
    "500009": t("Could not create tokenization application") + " (500009)",
    "500010": t("Could not upload tokenization application files") + " (500010)",
    "500011": t("Could not create tokenization application files record") + " (500011)",
    "500012": t("Database error") + " (500012)",
    "500013": t("Could not get file from S3") + " (500013)",
    "500014": t("Could not pin file to Pinata") + " (500014)",
    "500015": t("Could not marshal EstateTokenMetadata JSON") + " (500015)",
    "500016": t("Could not encrypt broker password") + " (500016)",
    "500017": t("Could not send email via Sendgrid") + " (500017)",
    "500018": t("Email verification code expiredAt is null") + " (500018)",
    "500019": t("Could not get broker ID from Gin Context") + " (500019)",
    "500020": t("Invalid broker ID from Gin Context") + " (500020)",
    "500021": t("Could not open national ID card image file") + " (500021)",
    "500022": t("Could not upload national ID card image file") + " (500022)",
    "500023": t("Could not create file record") + " (500023)",
    "500024": t("Could not override user KYC data") + " (500024)",
    "500025": t("Could not open avatar image file") + " (500025)",
    "500026": t("Could not upload avatar image file") + " (500026)",
    "500027": t("Could not override user data") + " (500027)",
    "500028": t("Could not find estate") + " (500028)",
    "500029": t("Could not create appraisal document files") + " (500029)",
    "500030": t("Could not create land registry document files") + " (500030)",
  }

  const handleError = (error: any, defaultMessage?: string) => {
    if (!(error instanceof Error) || !error?.message) {
      showError(defaultMessage || t("An error has occurred!"))
      return
    }
    const message = errorMessages[error.message]
    if (message) {
      showError(message)
    } else {
      // Fallback for undefined errors
      showError(defaultMessage || `${t("An error has occurred")}: ${error.message}`)
    }
  }

  return { handleError }
}

const errorMap: { [key: string]: Error } = {
  // 400 Errors
  "400001": Errors.BadRequestBindMultipartFormError,
  "400002": Errors.BadRequestInvalidRequestError,
  "400003": Errors.BadRequestInvalidNonceError,
  "400004": Errors.BadRequestInvalidSignatureError,
  "400006": Errors.BadRequestParseMultipartFormError,
  "400007": Errors.BadRequestMultipartFileError,
  "400008": Errors.BadRequestMultipartFormError,
  "400009": Errors.BadRequestAtLeast2CredentialPhotosError,
  "400010": Errors.BadRequestAtLeast4EstatePhotosError,
  "400011": Errors.BadRequestZoneNotSupportedError,
  "400012": Errors.BadRequestBindPaginationError,
  "400013": Errors.BadRequestInvalidPathParamError,
  "400014": Errors.BadRequestWrongBrokerEmailCodeError,
  "400015": Errors.BadRequestBindFilterQueryError,
  "400016": Errors.BadRequestInvalidQueryParamError,
  "400017": Errors.BadRequestUnavailableCurrencyError,
  "400018": Errors.BadRequestInvalidUnitPriceError,
  "400019": Errors.BadRequestBrokerEmailVerifiedError,
  "400020": Errors.BadRequestBrokerEmailCodeExpiredError,

  // 401 Errors
  "401001": Errors.UnauthorizedInvalidAuthHeaderError,
  "401002": Errors.UnauthorizedInvalidJWTError,
  "401003": Errors.UnauthorizedMalformedJWTError,
  "401004": Errors.UnauthorizedWrongPasswordError,

  // 403 Errors
  "403001": Errors.ForbiddenError,
  "403002": Errors.ForbiddenUserVerifiedError,

  // 404 Errors
  "404001": Errors.NotFoundNonceError,
  "404002": Errors.NotFoundCurrencyError,
  "404003": Errors.NotFoundBrokerWalletError,
  "404004": Errors.NotFoundEstateTokenMetadataError,
  "404005": Errors.NotFoundEstateTokenApplicationError,
  "404006": Errors.NotFoundBrokerEmailError,

  // 500 Errors
  "500000": Errors.InternalError,
  "500001": Errors.InternalErrorGenerateNonceError,
  "500002": Errors.InternalErrorSetNonceRedisError,
  "500003": Errors.InternalErrorGetNonceRedisError,
  "500004": Errors.InternalErrorMakeJWTStringError,
  "500005": Errors.InternalErrorMyAddressGinCtxError,
  "500006": Errors.InternalErrorMyAddressGinCtxValidError,
  "500007": Errors.InternalErrorGetOrCreateMeError,
  "500008": Errors.InternalErrorCreateEstateTokenMetadataError,
  "500009": Errors.InternalErrorCreateTokenizationAppError,
  "500010": Errors.InternalErrorUploadTokenizationAppFilesError,
  "500011": Errors.InternalErrorCreateTokenizationAppFilesError,
  "500012": Errors.InternalErrorDatabaseError,
  "500013": Errors.InternalErrorGetFileS3Error,
  "500014": Errors.InternalErrorPinFilePinataError,
  "500015": Errors.InternalErrorMarshalEstateTokenJSONError,
  "500016": Errors.InternalErrorEncryptBrokerPasswordError,
  "500017": Errors.InternalErrorSendEmailError,
  "500018": Errors.InternalErrorEmailVerificationNilError,
  "500019": Errors.InternalErrorBrokerIDGinCtxError,
  "500020": Errors.InternalErrorInvalidBrokerIDGinCtxError,
  "500021": Errors.InternalErrorOpenNationalIDCardImageError,
  "500022": Errors.InternalErrorUploadNationalIDCardImageError,
  "500023": Errors.InternalErrorCreateFileRecordError,
  "500024": Errors.InternalErrorOverrideUserKYCDataError,
  "500025": Errors.InternalErrorOpenAvatarImageError,
  "500026": Errors.InternalErrorUploadAvatarImageError,
  "500027": Errors.InternalErrorOverrideUserDataError,
  "500028": Errors.InternalErrorFindEstateError,
  "500029": Errors.InternalErrorCreateAppraisalDocumentsError,
  "500030": Errors.InternalErrorCreateLandRegistryDocumentsError,
}

export const checkErrorCode = (errorCode: string) => {
  if (errorMap[errorCode]) {
    throw errorMap[errorCode]
  }
}
