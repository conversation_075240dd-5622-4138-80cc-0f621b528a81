{"_format": "hh-sol-artifact-1", "contractName": "ERC20Upgradeable", "sourceName": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}