import { useEffect, useState, useCallback } from "react"
import useAuthS<PERSON> from "@/stores/authStore"
import { getPro<PERSON>le<PERSON><PERSON>, refreshTokenApi } from "@/service"
import * as SplashScreen from "expo-splash-screen"
import { getData, STORAGE_KEYS } from "@/utils/storage"
import { useFonts } from "expo-font"

import InterBold from "assets/fonts/Inter-Bold.ttf"
import InterMedium from "assets/fonts/Inter-Medium.ttf"
import InterRegular from "assets/fonts/Inter-Regular.ttf"
import InterSemiBold from "assets/fonts/Inter-SemiBold.ttf"
import { WalletAddress } from "@/service/types"

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync()

const usePrepareApp = () => {
  const { setIsAuthenticated, setUser, setAddress } = useAuthStore()
  const [isRefreshed, setIsRefreshed] = useState(false)
  const [loaded, error] = useFonts({
    InterBold,
    InterMedium,
    InterRegular,
    InterSemiBold,
  })

  useEffect(() => {
    const handleTokenRefresh = async () => {
      try {
        // Get refresh token from storage using utility function
        const storedRefreshToken = await getData(STORAGE_KEYS.REFRESH_TOKEN)
        const storedAddress = await getData(STORAGE_KEYS.ADDRESS)
        if (!storedRefreshToken || !storedAddress) {
          setIsAuthenticated(false)
          return
        }

        // Use the existing refreshToken function from service
        const success = await refreshTokenApi(storedRefreshToken)
        if (success) {
          // Update authentication state
          const user = await getProfileApi(storedAddress)
          if (user) {
            setUser(user)
            setAddress(storedAddress as WalletAddress)
          } else {
            setIsAuthenticated(false)
          }
        } else {
          // If refresh failed, set not authenticated
          setIsAuthenticated(false)
        }
      } catch {
        setIsAuthenticated(false)
      } finally {
        // Tell the application to render
        setIsRefreshed(true)
      }
    }

    handleTokenRefresh()
  }, [setIsAuthenticated, setUser, setAddress])

  const appIsReady = isRefreshed && (loaded || error)

  const onLayoutRootView = useCallback(async () => {
    if (appIsReady) {
      // This tells the splash screen to hide immediately
      await SplashScreen.hideAsync()
    }
  }, [appIsReady])

  return { appIsReady, onLayoutRootView }
}

export default usePrepareApp
