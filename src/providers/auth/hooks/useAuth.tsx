import { PROVIDER_CHAIN_ID } from "@/config/env"
import useAuthStore from "@/stores/authStore"
import Logger from "@/utils/logger"
import { clearStoredToken } from "@/utils/storage"
import { useCallback, useEffect } from "react"
import { useAccount, useAccountEffect, useChainId, useSwitchChain } from "wagmi"

// Create a logger instance for auth operations
const logger = new Logger({ tag: "Auth" })

export const useAuth = () => {
  const { address } = useAccount()
  const { setAddress, setUser, isAuthenticated } = useAuthStore()
  const selectedNetworkId = useChainId()
  const { switchChainAsync } = useSwitchChain()

  const needSwitchNetwork = !(selectedNetworkId === PROVIDER_CHAIN_ID)

  const handleSwitchNetwork = useCallback(async () => {
    try {
      const result = await switchChainAsync({ chainId: PROVIDER_CHAIN_ID })
      if (result.id !== PROVIDER_CHAIN_ID) {
        throw new Error(result.name)
      }
      logger.success("Switch network successful")
      return true
    } catch {
      logger.warn("Switch network failed")
      return false
    }
  }, [switchChainAsync])

  useEffect(() => {
    if (isAuthenticated && address && needSwitchNetwork) {
      handleSwitchNetwork()
    }
  }, [address, isAuthenticated, needSwitchNetwork, handleSwitchNetwork])

  useAccountEffect({
    onDisconnect() {
      setAddress(undefined)
      setUser(undefined)
      clearStoredToken()
    },
  })
}
