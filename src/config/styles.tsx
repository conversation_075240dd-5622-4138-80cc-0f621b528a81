import { StyleSheet } from "react-native"
import Colors from "./colors"

const textStyles = StyleSheet.create({
  size5XLRegular: {
    fontSize: 42,
    lineHeight: 50,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  size5XLMedium: {
    fontSize: 42,
    lineHeight: 50,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  size5XLSemiBold: {
    fontSize: 42,
    lineHeight: 50,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  size5XLBold: {
    fontSize: 42,
    lineHeight: 50,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
  size4XLRegular: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  size4XLMedium: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  size4XLSemiBold: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  size4XLBold: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
  size3XLRegular: {
    fontSize: 24,
    lineHeight: 29,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  size3XLMedium: {
    fontSize: 24,
    lineHeight: 29,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  size3XLSemiBold: {
    fontSize: 24,
    lineHeight: 29,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  size3XLBold: {
    fontSize: 24,
    lineHeight: 29,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
  size2XLRegular: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  size2XLMedium: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  size2XLSemiBold: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  size2XLBold: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
  XLRegular: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  XLMedium: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  XLSemiBold: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  XLBold: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
  LRegular: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  LMedium: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  LSemiBold: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  LBold: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
  MRegular: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  MMedium: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  MSemiBold: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  MBold: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
  SRegular: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  SMedium: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  SSemiBold: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  SBold: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
  XSRegular: {
    fontSize: 8,
    lineHeight: 10,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  XSMedium: {
    fontSize: 8,
    lineHeight: 10,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  XSSemiBold: {
    fontSize: 8,
    lineHeight: 10,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  XSBold: {
    fontSize: 8,
    lineHeight: 10,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
  size2XSRegular: {
    fontSize: 7,
    lineHeight: 8,
    fontWeight: "400",
    fontFamily: "InterRegular",
    color: Colors.PalleteWhite,
  },
  size2XSMedium: {
    fontSize: 7,
    lineHeight: 8,
    fontWeight: "500",
    fontFamily: "InterMedium",
    color: Colors.PalleteWhite,
  },
  size2XSSemiBold: {
    fontSize: 7,
    lineHeight: 8,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: Colors.PalleteWhite,
  },
  size2XSBold: {
    fontSize: 7,
    lineHeight: 8,
    fontWeight: "700",
    fontFamily: "InterBold",
    color: Colors.PalleteWhite,
  },
})

const viewStyles = StyleSheet.create({
  dropdownItemContainer: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 12,
  },
  size48Icon: {
    width: 48,
    height: 48,
  },
  size24Icon: {
    width: 24,
    height: 24,
  },
  size22Icon: {
    width: 22,
    height: 22,
  },
  size20Icon: {
    width: 20,
    height: 20,
  },
  size18Icon: {
    width: 18,
    height: 18,
  },
  size16Icon: {
    width: 16,
    height: 16,
  },
  size14Icon: {
    width: 14,
    height: 14,
  },
  size12Icon: {
    width: 12,
    height: 12,
  },
  size12x8Icon: {
    width: 12,
    height: 8,
  },
  size10Icon: {
    width: 10,
    height: 10,
  },
  size8Icon: {
    width: 8,
    height: 8,
  },
})

export { textStyles, viewStyles }
