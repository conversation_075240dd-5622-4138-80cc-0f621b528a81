export const PROVIDER_CHAIN_ID = Number(process.env.EXPO_PUBLIC_PROVIDER_CHAINID)
export const BASE_API_URL = process.env.EXPO_PUBLIC_BASE_API_URL
export const BASE_WEB_URL = process.env.EXPO_PUBLIC_BASE_WEB_URL
export const REDIRECT_URL = process.env.EXPO_PUBLIC_REDIRECT_URL
export const WALLET_CONNECT_PROJECT_ID = process.env.EXPO_PUBLIC_WALLET_CONNECT_PROJECT_ID as string
export const CONTRACT_ADDRESS_LIQUIDATION_CURRENCY = process.env
  .EXPO_PUBLIC_LIQUIDATION_CURRENCY_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_PRIMARY_TOKEN = process.env
  .EXPO_PUBLIC_PRIMARY_TOKEN_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_STAKE_TOKEN_1 = process.env
  .EXPO_PUBLIC_STAKE_TOKEN_1_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_STAKE_TOKEN_2 = process.env
  .EXPO_PUBLIC_STAKE_TOKEN_2_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_STAKE_TOKEN_3 = process.env
  .EXPO_PUBLIC_STAKE_TOKEN_3_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_MORTGAGE_TOKEN = process.env
  .EXPO_PUBLIC_MORTGAGE_TOKEN_ADDRESS as `0x${string}`
export const CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE = process.env
  .EXPO_PUBLIC_MORTGAGE_MARKETPLACE_ADDRESS as `0x${string}`
