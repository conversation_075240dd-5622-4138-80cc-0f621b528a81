// loadingStore.ts

import { create } from "zustand"

interface LoadingState {
  isLoading: boolean
  setLoading: (loading: boolean) => void
  showCancelBtn: boolean
  setShowCancelBtn: (show: boolean) => void
}

const useLoadingStore = create<LoadingState>((set) => ({
  isLoading: false,
  setLoading: (loading: boolean) => set({ isLoading: loading }),
  showCancelBtn: false,
  setShowCancelBtn: (show: boolean) => set({ showCancelBtn: show }),
}))

export default useLoadingStore
