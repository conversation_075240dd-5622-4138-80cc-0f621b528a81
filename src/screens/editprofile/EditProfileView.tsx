import React, { useCallback } from "react"
import { Image, ScrollView, StyleSheet, Text, View } from "react-native"
import { Background, TopBar, CustomPressable, InputField } from "src/components"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { ActivityIndicator } from "react-native-paper"
import { useTranslation } from "react-i18next"
import { Controller } from "react-hook-form"
import { PrimaryButton } from "src/components/Button"
import { useAccount } from "wagmi"
import { useEditProfileFormSchema, useEditProfile } from "./hooks/useEditProfile"
import { useChoosePhoto } from "src/utils/choosePhotoExt"
import { ImagePickerAsset } from "expo-image-picker/src/ImagePicker.types"
import { LabelView } from "src/components/LabelView"
import icCircleClose from "assets/images/ic_circle_close.png"
import icUpload from "assets/images/ic_upload.png"
import icCopy from "assets/images/ic_copy.png"
import { shortenAddress } from "@/utils"
import * as Clipboard from "expo-clipboard"
import { showSuccess } from "@/utils/toast"

const ImageUploader: React.FC<{
  imageUri?: string
  onPress: () => void
  onClear: () => void
}> = ({ imageUri, onPress, onClear }) => {
  const { t } = useTranslation()

  return (
    <View style={styles.imageContainer}>
      <View>
        {imageUri ? (
          <CustomPressable style={styles.imageContent} onPress={onPress}>
            <Image source={{ uri: imageUri }} style={styles.image} />
            <CustomPressable onPress={onClear} style={styles.close}>
              <Image source={icCircleClose} style={viewStyles.size16Icon} />
            </CustomPressable>
          </CustomPressable>
        ) : (
          <CustomPressable style={styles.upload} onPress={onPress}>
            <Image source={icUpload} style={viewStyles.size16Icon} />
            <Text style={styles.uploadFile}>{t("Upload file")}</Text>
          </CustomPressable>
        )}
      </View>
    </View>
  )
}

const EditProfileView: React.FC = () => {
  const { t } = useTranslation()
  const { address } = useAccount()
  const { onSubmit, isLoading, profile } = useEditProfile(address || "")

  const { handleChoosePhoto } = useChoosePhoto()
  const onChoosePhoto = useCallback(
    (onChange: (value: ImagePickerAsset | undefined) => void) => {
      handleChoosePhoto((files) => {
        onChange(files[0])
      })
    },
    [handleChoosePhoto],
  )

  const handleCopyWalletAddress = async () => {
    if (!address) return
    await Clipboard.setStringAsync(address)
    showSuccess(t("Address copied"))
  }

  const { form } = useEditProfileFormSchema(profile || undefined)

  const shortAddress = shortenAddress(profile?.address || "")

  return (
    <Background>
      <TopBar enableBack={true} title={t("Edit profile")} />
      <View style={styles.mainContainer}>
        <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            <LabelView label={t("Wallet")} style={styles.nationalityLabel} />
            <View style={styles.walletRow}>
              <Text style={{ color: Colors.Neutral300 }}>{shortAddress}</Text>
              <CustomPressable onPress={handleCopyWalletAddress}>
                <Image source={icCopy} style={viewStyles.size16Icon} />
              </CustomPressable>
            </View>
            <Controller
              control={form.control}
              name="fullName"
              render={({ field: { onChange, onBlur, value } }) => (
                <InputField
                  label={t("Display name")}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  style={styles.marginTop16}
                />
              )}
            />
            <Controller
              control={form.control}
              name="email"
              render={({ field: { onChange, onBlur, value } }) => (
                <InputField
                  label={t("Email")}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  style={styles.marginTop16}
                  error={
                    form.formState.errors.email?.message &&
                    String(form.formState.errors.email?.message)
                  }
                />
              )}
            />
            <Controller
              control={form.control}
              name="phone"
              render={({ field: { onChange, onBlur, value } }) => (
                <InputField
                  label={t("Phone")}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  style={styles.marginTop16}
                  error={
                    form.formState.errors.phone?.message &&
                    String(form.formState.errors.phone?.message)
                  }
                />
              )}
            />
            <Controller
              control={form.control}
              name="avatar"
              render={({ field: { onChange, value } }) => {
                const imagePickerAsset = value as ImagePickerAsset | undefined
                return (
                  <>
                    <LabelView label={t("Avatar")} style={styles.marginTop16} />
                    <ImageUploader
                      imageUri={imagePickerAsset?.uri}
                      onPress={() => onChoosePhoto(onChange)}
                      onClear={() => {
                        onChange(undefined)
                      }}
                    />
                  </>
                )
              }}
            />
          </View>
        </ScrollView>

        <PrimaryButton
          title={t("Save")}
          onPress={form.handleSubmit(onSubmit)}
          color={Colors.Primary500}
          contentColor={Colors.PalleteBlack}
          style={styles.editProfile}
          textStyle={textStyles.LMedium}
          width={"90%"}
          height={38}
          isLoading={isLoading}
          icon={isLoading && <ActivityIndicator size="small" color={Colors.PalleteWhite} />}
        />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 12,
  },
  content: {
    paddingBottom: 16,
  },
  upload: {
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadView: {
    backgroundColor: Colors.Primary500,
    padding: 12,
    borderRadius: 8,
  },
  close: {
    position: "absolute",
    right: 0,
    padding: 4,
  },
  nationalityLabel: {
    marginTop: 16,
    marginBottom: 4,
  },
  marginTop16: {
    marginTop: 16,
  },
  walletRow: {
    backgroundColor: Colors.Neutral900,
    borderRadius: 6,
    flexDirection: "row",
    paddingHorizontal: 12,
    justifyContent: "space-between",
    height: 38,
    alignItems: "center",
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  imageContainer: {
    width: "100%",
    aspectRatio: 20 / 9,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  image: {
    width: "100%",
    height: "100%",
    borderRadius: 8,
    aspectRatio: 1 / 1,
  },
  dropdown: {
    marginTop: 8,
    height: 38,
    width: "100%",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    backgroundColor: Colors.Neutral900,
    paddingEnd: 12,
    ...textStyles.MMedium,
  },
  dropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 12,
  },
  disableDropdownItem: {
    ...textStyles.MMedium,
    color: Colors.Neutral300,
    backgroundColor: Colors.Neutral900,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 12,
  },
  editProfile: {
    width: "100%",
    alignSelf: "center",
    marginBottom: 16,
  },
  uploadFile: {
    ...textStyles.SMedium,
    color: Colors.PalleteWhite,
    marginTop: 8,
  },
  imageContent: {
    alignItems: "center",
  },
})

export default EditProfileView
