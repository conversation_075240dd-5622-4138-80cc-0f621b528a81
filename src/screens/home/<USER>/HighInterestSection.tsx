import React from "react"
import { View, StyleSheet } from "react-native"
import HomeHeader from "./HomeHeader"
import HighInterestItem from "./HighInterestItem"
import { MortgageLoan } from "@/service/types"
interface HighInterestSectionProps {
  title: string
  data: MortgageLoan[]
}

const HighInterestSection: React.FC<HighInterestSectionProps> = ({ title, data = [] }) => {
  return (
    <>
      {data.length > 0 && (
        <View style={styles.container}>
          <HomeHeader title={title} isShowExplore={false} />
          <ListHighInterestView data={data} />
        </View>
      )}
    </>
  )
}

interface ListHighInterestViewProps {
  data: MortgageLoan[]
}

const ListHighInterestView: React.FC<ListHighInterestViewProps> = ({ data }) => {
  return (
    <>
      {data.map((item) => (
        <HighInterestItem key={item.id} item={item} />
      ))}
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
})

export default HighInterestSection
