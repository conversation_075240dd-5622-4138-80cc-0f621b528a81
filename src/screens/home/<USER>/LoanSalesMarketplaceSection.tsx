import React from "react"
import { View, StyleSheet, ViewStyle } from "react-native"
import { Offer } from "@/service/types"
import { calculateGridItemDimensions } from "@/utils/layout"
import HomeHeader from "./HomeHeader"
import { GridLoanSaleMarketplaceItem } from "@/screens/shared/components"
import * as Routes from "src/navigation/Routers"
import { useTabNavigaton } from "src/navigation/Routers"

const { itemWidth, itemSpacing } = calculateGridItemDimensions({
  numColumns: 2,
})
interface LoanSalesMarketplaceSectionProps {
  title: string
  data: Offer[]
}

const LoanSalesMarketplaceSection: React.FC<LoanSalesMarketplaceSectionProps> = ({
  title,
  data = [],
}) => {
  const navigation = useTabNavigaton()
  const handlePressExplore = () => {
    navigation.navigate(Routes.LOAN_SALE)
  }

  return (
    <>
      {data.length > 0 && (
        <View style={styles.container}>
          <HomeHeader title={title} isShowExplore={true} onPressExplore={handlePressExplore} />
          <GridLoanSaleMarketplaceView data={data} />
        </View>
      )}
    </>
  )
}
interface GridLoanSaleMarketplaceViewProps {
  data: Offer[]
}

const GridLoanSaleMarketplaceView: React.FC<GridLoanSaleMarketplaceViewProps> = ({ data }) => {
  return (
    <View style={styles.containerGrid}>
      {data.map((item, index) => {
        const itemStyle: ViewStyle = {
          ...styles.itemContainer,
          ...(index % 2 === 0 ? styles.itemMarginEnd : {}),
        }
        return <GridLoanSaleMarketplaceItem key={item.id} item={item} style={itemStyle} />
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  containerGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  itemContainer: {
    width: itemWidth,
  },
  itemMarginEnd: {
    width: itemWidth,
    marginEnd: itemSpacing,
  },
})

export default LoanSalesMarketplaceSection
