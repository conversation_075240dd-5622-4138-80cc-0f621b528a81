import { MortgageLoan } from "@/service/types"
import React from "react"
import { View, StyleSheet, ViewStyle } from "react-native"
import HomeHeader from "./HomeHeader"
import { GridOpenLoanListingItem } from "src/screens/shared/components"
import { calculateGridItemDimensions } from "@/utils/layout"
import * as Routes from "src/navigation/Routers"
import { useTabNavigaton } from "src/navigation/Routers"

const { itemWidth, itemSpacing } = calculateGridItemDimensions({
  numColumns: 2,
})
interface OpenLoanListingsSectionProps {
  title: string
  data: MortgageLoan[]
}

const OpenLoanListingsSection: React.FC<OpenLoanListingsSectionProps> = ({ title, data = [] }) => {
  const navigation = useTabNavigaton()
  const handlePressExplore = () => {
    navigation.navigate(Routes.ALL_LOANS)
  }

  return (
    <>
      {data.length > 0 && (
        <View style={styles.container}>
          <HomeHeader title={title} isShowExplore={true} onPressExplore={handlePressExplore} />
          <GridOpenLoanListingsView data={data} />
        </View>
      )}
    </>
  )
}

interface GridOpenLoanListingsViewProps {
  data: MortgageLoan[]
}

const GridOpenLoanListingsView: React.FC<GridOpenLoanListingsViewProps> = ({ data }) => {
  return (
    <View style={styles.containerGrid}>
      {data.map((item, index) => {
        const itemStyle: ViewStyle = {
          ...styles.itemContainer,
          ...(index % 2 === 0 ? styles.itemMarginEnd : {}),
        }
        return <GridOpenLoanListingItem key={item.id} item={item} style={itemStyle} />
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  containerGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  itemContainer: {
    width: itemWidth,
  },
  itemMarginEnd: {
    width: itemWidth,
    marginEnd: itemSpacing,
  },
})

export default OpenLoanListingsSection
