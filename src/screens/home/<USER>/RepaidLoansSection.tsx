import { MortgageLoan } from "@/service/types"
import React from "react"
import { View, StyleSheet, ViewStyle } from "react-native"
import HomeHeader from "./HomeHeader"
import { GridRepaidLoansItem } from "@/screens/shared/components"
import { calculateGridItemDimensions } from "@/utils/layout"

const { itemWidth, itemSpacing } = calculateGridItemDimensions({
  numColumns: 2,
})
interface RepaidLoansSectionProps {
  title: string
  data: MortgageLoan[]
}

const RepaidLoansSection: React.FC<RepaidLoansSectionProps> = ({ title, data = [] }) => {
  return (
    <>
      {data.length > 0 && (
        <View style={styles.container}>
          <HomeHeader title={title} isShowExplore={true} />
          <GridRepaidLoansView data={data} />
        </View>
      )}
    </>
  )
}
interface GridRepaidLoansViewProps {
  data: MortgageLoan[]
}

const GridRepaidLoansView: React.FC<GridRepaidLoansViewProps> = ({ data }) => {
  return (
    <View style={styles.containerGrid}>
      {data.map((item, index) => {
        const itemStyle: ViewStyle = {
          ...styles.itemContainer,
          ...(index % 2 === 0 ? styles.itemMarginEnd : {}),
        }
        return <GridRepaidLoansItem key={item.id} item={item} style={itemStyle} />
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  containerGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  itemContainer: {
    width: itemWidth,
  },
  itemMarginEnd: {
    width: itemWidth,
    marginEnd: itemSpacing,
  },
})

export default RepaidLoansSection
