import React from "react"
import { View, Text, Image, StyleSheet } from "react-native"
import { MortgageLoan } from "@/service/types"
import { CardView } from "@/components/CardView"
import Colors from "@/config/colors"
import { textStyles } from "@/config/styles"
import { useCurrencies } from "@/screens/shared/hooks/useCurrencies"
import InfoItem from "@/screens/shared/components/InfoItem"
import icClock9 from "assets/images/ic_clock_9.png"
import icMapPin from "assets/images/ic_map_pin.png"
import icBoxLock from "assets/images/ic_box_lock.png"
import { useTranslation } from "react-i18next"
import { CustomPressable } from "@/components"
import { convertDateFromTimeStamp, DateTimeFormat } from "@/utils/timeExt"
import { formatCurrencyByDecimals } from "src/utils/format"
import * as Routes from "src/navigation/Routers"
import { useAppNavigaton } from "src/navigation/Routers"
import { CurrencyView } from "@/screens/shared/components/CurrencyView"

interface HighInterestItemProps {
  item: MortgageLoan
}

const HighInterestItem = ({ item }: HighInterestItemProps) => {
  const { t } = useTranslation()

  const {
    id,
    dueInSeconds,
    currency,
    principal,
    apr,
    mortgageAmount,
    estate: {
      decimals,
      metadata: {
        imageUrl,
        metadata: {
          name,
          locale_detail: { zone },
        },
      },
    },
  } = item

  const navigation = useAppNavigaton()

  const { tokenSymbol, tokenImageUrl } = useCurrencies(currency)

  const displayDueDate = `${t("Due date")} ${convertDateFromTimeStamp(dueInSeconds * 1000, DateTimeFormat.SHORT)}`

  const formattedMortgageAmount = formatCurrencyByDecimals(mortgageAmount, decimals)
  const displayCollateral = `${t("Collateral")} ${formattedMortgageAmount} NFTs`

  const displayPrincipal = `${t("Principal")} ${formatCurrencyByDecimals(principal, decimals)}`
  const displayApr = `${t("APR")} ${apr}%`

  const onNavigateDetails = () => {
    navigation.navigate(Routes.LOAN_DETAIL, { id })
  }

  return (
    <CardView style={styles.card}>
      <CustomPressable style={styles.container} onPress={onNavigateDetails}>
        <Image source={{ uri: imageUrl }} style={styles.image} />

        <View style={styles.contentContainer}>
          {dueInSeconds > 0 && <InfoItem icon={icClock9} text={displayDueDate} />}

          <View>
            <Text style={styles.title} numberOfLines={1}>
              {name}
            </Text>
            <View style={styles.row}>
              <InfoItem icon={icMapPin} text={zone} />
              <Text style={styles.dot}>•</Text>
              <InfoItem icon={icBoxLock} text={displayCollateral} />
            </View>
          </View>

          <View style={styles.row}>
            <CurrencyView
              textValue={displayPrincipal}
              tokenSymbol={tokenSymbol}
              tokenImageUrl={tokenImageUrl}
              style={styles.principal}
              imageStyle={styles.principalImage}
            />
            <Text style={styles.apr}>{displayApr}</Text>
          </View>
        </View>
      </CustomPressable>
    </CardView>
  )
}

const styles = StyleSheet.create({
  card: {
    flex: 1,
    marginBottom: 12,
  },
  container: {
    flex: 1,
    gap: 8,
    padding: 8,
    flexDirection: "row",
    alignItems: "center",
  },
  image: {
    height: 80,
    width: 130,
    borderRadius: 4,
    resizeMode: "cover",
  },
  contentContainer: {
    flex: 1,
    gap: 9,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  title: {
    ...textStyles.MMedium,
    marginBottom: 4,
  },
  dot: {
    color: Colors.PalleteWhite,
    marginHorizontal: 4,
  },
  principal: {
    backgroundColor: Colors.Neutral900,
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 4,
    marginRight: 8,
  },
  principalImage: {
    marginLeft: 2,
  },
  apr: {
    ...textStyles.SMedium,
    color: Colors.Primary300,
    backgroundColor: Colors.Primary900,
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 4,
  },
})

export default HighInterestItem
