import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { FeaturedData } from "../hooks/useHome"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "@/config/styles"
import { CustomPressable } from "@/components"
import Colors from "@/config/colors"
import icPlus from "assets/images/ic_plus.png"
import { useAppNavigaton } from "@/navigation/Routers"
import * as Routes from "@/navigation/Routers"
import useAuthStore from "@/stores/authStore"
import { useCurrencies } from "@/screens/shared/hooks/useCurrencies"
import { formatShortedCurrencyByDecimals } from "@/utils"

interface FeaturedSectionProps {
  title: string
  data: FeaturedData
}

const FeaturedSection: React.FC<FeaturedSectionProps> = ({ title, data }) => {
  const { t } = useTranslation()
  const { isAuthenticated } = useAuthStore()

  const {
    totalVolume: { currency, totalPrincipal },
    openLoans,
    completedLoans,
    description,
  } = data

  const { tokenSymbol } = useCurrencies(currency)

  const formattedTotalPrincipal = formatShortedCurrencyByDecimals(totalPrincipal, 18)
  const displayTotalVolume = `${formattedTotalPrincipal} ${tokenSymbol}`

  const navigation = useAppNavigaton()
  const navigateToLoanCreate = () => {
    navigation.navigate(Routes.CREATE_LOAN)
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>

        <Text style={styles.description}>{description}</Text>

        {isAuthenticated && (
          <CustomPressable style={styles.button} onPress={navigateToLoanCreate}>
            <Image source={icPlus} style={styles.buttonIcon} />
            <Text style={styles.buttonText}>{t("Create Loan")}</Text>
          </CustomPressable>
        )}

        <View style={styles.statsContainer}>
          <StatItem label={t("Total Volume")} value={displayTotalVolume} />

          <StatItem label={t("Open Loans")} value={openLoans.toString()} />

          <StatItem label={t("Completed Loans")} value={completedLoans.toString()} />
        </View>
      </View>
    </View>
  )
}

const StatItem: React.FC<{ label: string; value: string }> = ({ label, value }) => {
  return (
    <View style={styles.statItem}>
      <Text style={textStyles.MMedium}>{label}</Text>
      <Text style={textStyles.LMedium}>{value}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 6,
    paddingHorizontal: 16,
    paddingVertical: 48,
  },
  content: {
    flex: 1,
    paddingTop: 51,
  },
  title: {
    ...textStyles.size2XLMedium,
    marginBottom: 16,
  },
  description: {
    ...textStyles.MMedium,
    paddingBottom: 16,
    marginBottom: 16,
  },
  button: {
    backgroundColor: Colors.Success300,
    borderRadius: 999,
    paddingHorizontal: 8,
    paddingVertical: 6,
    gap: 4,
    alignSelf: "flex-start",
    flexDirection: "row",
    marginBottom: 51,
  },
  buttonIcon: {
    ...viewStyles.size14Icon,
    tintColor: Colors.PalleteBlack,
  },
  buttonText: {
    ...textStyles.MMedium,
    color: Colors.PalleteBlack,
  },
  statsContainer: {
    flexDirection: "row",
    gap: 24,
  },
  statItem: {
    gap: 6,
  },
})

export default FeaturedSection
