import React from "react"
import { <PERSON>List, <PERSON><PERSON>reshControl, StyleSheet, View } from "react-native"
import { useHomeContext } from "./context/HomeContext"
import { Section, SectionType } from "./hooks/useHome"
import {
  FeaturedSection,
  HighInterestSection,
  OpenLoanListingsSection,
  LoanSalesMarketplaceSection,
  RepaidLoansSection,
} from "./components"
import { Background, EmptyView } from "@/components"
import { useTranslation } from "react-i18next"

const HomeView: React.FC = (): React.ReactElement => {
  const { sections, isLoading, isErrorAll, isAllHasNoData, handleRefresh } = useHomeContext()
  const { t } = useTranslation()
  const renderSectionContent = (section: Section) => {
    switch (section.type) {
      case SectionType.FEATURED:
        return <FeaturedSection title={section.title} data={section.data} />
      case SectionType.HIGH_INTEREST:
        return <HighInterestSection title={section.title} data={section.data} />
      case SectionType.OPEN_LOAN_LISTINGS:
        return <OpenLoanListingsSection title={section.title} data={section.data} />
      case SectionType.LOAN_SALES_MARKETPLACE:
        return <LoanSalesMarketplaceSection title={section.title} data={section.data} />
      case SectionType.REPAID_LOANS:
        return <RepaidLoansSection title={section.title} data={section.data} />
    }
  }

  const renderContentView = () => {
    if (isLoading) {
      return null
    }
    if (isErrorAll) {
      return <EmptyView subtitle={t("Failed to load home data")} />
    }
    if (isAllHasNoData) {
      return <EmptyView subtitle={t("No home data")} />
    }
    return (
      <View style={styles.container}>
        <FlatList
          data={sections}
          renderItem={({ item }) => renderSectionContent(item)}
          keyExtractor={(item) => item.type}
          showsVerticalScrollIndicator={false}
          refreshControl={<RefreshControl refreshing={isLoading} onRefresh={handleRefresh} />}
        />
      </View>
    )
  }

  return <Background>{renderContentView()}</Background>
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
})

export default HomeView
