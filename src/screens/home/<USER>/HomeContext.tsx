import { createContext, useContext } from "react"
import { Section } from "../hooks/useHome"

interface HomeContextType {
  sections: Section[]
  isLoading: boolean
  isErrorAll: boolean
  isAllHasNoData: boolean
  handleRefresh: () => void
}

export const HomeContext = createContext<HomeContextType>({
  sections: [],
  isLoading: false,
  isErrorAll: false,
  isAllHasNoData: false,
  handleRefresh: () => {},
})

export const useHomeContext = () => {
  return useContext(HomeContext)
}
