import React from "react"
import { StyleSheet, Text, View } from "react-native"
import Colors from "@/config/colors"
import { CustomPressable } from "@/components"
import { ExpandView } from "@/components"
import { textStyles } from "@/config/styles"
import { useTranslation } from "react-i18next"

interface HomeHeaderProps {
  title: string
  isShowExplore?: boolean
  onPressExplore?: () => void
}

const HomeHeader: React.FC<HomeHeaderProps> = ({
  title,
  isShowExplore = true,
  onPressExplore = () => {},
}) => {
  const { t } = useTranslation()
  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>{t(title)}</Text>

      <ExpandView />

      {isShowExplore && (
        <CustomPressable onPress={onPressExplore}>
          <Text style={styles.exploreText}>{t("Explore")}</Text>
        </CustomPressable>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 16,
  },
  sectionTitle: {
    ...textStyles.size2XLMedium,
    color: Colors.PalleteWhite,
  },
  exploreText: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
})

export default HomeHeader
