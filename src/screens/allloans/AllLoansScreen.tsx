import React from "react"
import { useAllLoans } from "./hooks/useAllLoans"
import AllLoansView from "./AllLoansView"
import { AllLoansContext } from "./context/AllLoansContext"

const AllLoansScreen: React.FC = () => {
  const { contextValue } = useAllLoans()

  return (
    <AllLoansContext.Provider value={contextValue}>
      <AllLoansView />
    </AllLoansContext.Provider>
  )
}

export default AllLoansScreen
