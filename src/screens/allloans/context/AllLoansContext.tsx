import { createContext, useContext } from "react"
import { MortgageLoan } from "@/service/types"
interface AllLoansContextType {
  loans: MortgageLoan[]
  totalLoans: number
  isLoading: boolean
  error: Error | null
  handleRefresh: () => void
}

export const AllLoansContext = createContext<AllLoansContextType>({
  loans: [],
  totalLoans: 0,
  isLoading: false,
  error: null,
  handleRefresh: () => {},
})

export const useAllLoansContext = () => {
  return useContext(AllLoansContext)
}
