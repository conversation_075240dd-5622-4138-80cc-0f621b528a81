import React from "react"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { parseEther } from "@ethersproject/units"
import { maxUint256 } from "viem"
import { MortgageLoan } from "src/service/types"
import { mortgageTokenAbi } from "@/contracts/mortgage-token"
import { erc20Abi, useErc20Allowance } from "@/contracts"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { CONTRACT_ADDRESS_MORTGAGE_TOKEN } from "src/config/env"
import { showError, showSuccessWhenCallContract } from "@/utils/toast"
import Logger from "src/utils/logger"
import useLoadingStore from "@/stores/loadingStore"
import { Pressable } from "react-native"
import { PrimaryButton } from "@/components/Button"

const logger = new Logger({ tag: "LendLoanButton" })

interface LendLoanButtonProps {
  loan: MortgageLoan
  buttonContent?: React.ReactNode
  onRefresh?: () => void
}

const LendLoanButton: React.FC<LendLoanButtonProps> = ({ loan, buttonContent, onRefresh }) => {
  const { t } = useTranslation()
  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const { isLoading, setLoading } = useLoadingStore()

  const currencyAllowanceWei = useErc20Allowance(
    address as string,
    loan.currency as `0x${string}`,
    CONTRACT_ADDRESS_MORTGAGE_TOKEN,
  )

  const handleLend = async () => {
    if (isLoading || !ethersProvider) return
    setLoading(true)
    try {
      if (currencyAllowanceWei < BigInt(parseEther(loan.principal).toString())) {
        const txHash = await writeContractAsync({
          address: loan.currency as `0x${string}`,
          abi: erc20Abi,
          functionName: "approve",
          args: [CONTRACT_ADDRESS_MORTGAGE_TOKEN, maxUint256],
        })
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          showError(t("Failed to approve"))
          throw new Error("Fail to approve currency")
        }
      }
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
        abi: mortgageTokenAbi,
        functionName: "lend",
        args: [BigInt(loan.id), BigInt(loan.estate.id)],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Lend success") + ". " + t("Data will be updated in few seconds"),
        )
        onRefresh?.()
      } else {
        logger.error("Lend failed", txHash)
        showError(t("Lend failed"))
      }
    } catch (e) {
      showError(t("Lend failed" + e))
      logger.error("Failed to lend loan", {
        component: "TokenizationLoansView",
        action: "Lend",
        error: e,
      })
    } finally {
      setLoading(false)
    }
  }

  if (!address) {
    return null
  }

  return buttonContent ? (
    <Pressable onPress={handleLend}>{buttonContent}</Pressable>
  ) : (
    <PrimaryButton title={t("Lend now")} onPress={handleLend} />
  )
}

export { LendLoanButton }
