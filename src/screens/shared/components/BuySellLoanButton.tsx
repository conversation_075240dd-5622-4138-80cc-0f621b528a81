import React from "react"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { Offer } from "src/service/types"
import { mortgageMarketplaceAbi } from "@/contracts/mortgage-marketplace"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE } from "src/config/env"
import { showError, showSuccessWhenCallContract } from "@/utils/toast"
import Logger from "src/utils/logger"
import useLoadingStore from "@/stores/loadingStore"
import { Pressable } from "react-native"
import { PrimaryButton } from "@/components/Button"

interface BuySellLoanButtonProps {
  offer: Offer
  buttonContent: React.ReactNode
  onRefresh?: () => void
}

const logger = new Logger({ tag: "BuySellLoanButton" })

export const BuySellLoanButton: React.FC<BuySellLoanButtonProps> = ({
  offer,
  buttonContent,
  onRefresh,
}) => {
  const { t } = useTranslation()
  const { isLoading, setLoading } = useLoadingStore()
  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const handleBuy = async () => {
    if (isLoading || !ethersProvider || !CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE) return
    setLoading(true)
    try {
      // Using the buy function with offerId and tokenId
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE,
        abi: mortgageMarketplaceAbi,
        functionName: "buy",
        args: [BigInt(offer.id), BigInt(offer.tokenId)],
      })

      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Purchase success") + ". " + t("Data will be updated in few seconds"),
        )
        onRefresh?.()
      } else {
        showError(t("Purchase failed"))
      }
    } catch (e: any) {
      showError(t("Purchase failed"))
      logger.error("Buy loan failed", e)
    } finally {
      setLoading(false)
    }
  }

  // Don't show the button if the user is the seller or the offer is not in selling state
  if (
    !address ||
    !CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE ||
    address.toLowerCase() === offer.sellerAddress.toLowerCase() ||
    offer.state !== "SELLING"
  ) {
    return null
  }

  return buttonContent ? (
    <Pressable onPress={handleBuy}>{buttonContent}</Pressable>
  ) : (
    <PrimaryButton title={t("Buy")} onPress={handleBuy} />
  )
}
