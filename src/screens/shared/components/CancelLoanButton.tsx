import React from "react"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { CONTRACT_ADDRESS_MORTGAGE_TOKEN } from "src/config/env"
import { mortgageTokenAbi } from "src/contracts/mortgage-token"
import { MortgageLoan } from "src/service/types"
import { showError, showSuccessWhenCallContract } from "@/utils/toast"
import Logger from "src/utils/logger"
import useLoadingStore from "@/stores/loadingStore"
import { Pressable } from "react-native"
import { PrimaryButton } from "@/components/Button"
import Colors from "@/config/colors"

interface CancelLoanButtonProps {
  loan: MortgageLoan
  buttonContent: React.ReactNode
  onRefresh?: () => void
}

const logger = new Logger({ tag: "TokenizationLoansView" })

const CancelLoanButton: React.FC<CancelLoanButtonProps> = ({ loan, buttonContent, onRefresh }) => {
  const { t } = useTranslation()
  const { isLoading, setLoading } = useLoadingStore()

  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const handleCancel = async () => {
    if (isLoading || !ethersProvider) return
    setLoading(true)
    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
        abi: mortgageTokenAbi,
        functionName: "cancel",
        args: [BigInt(loan.id)],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Cancel success") + ". " + t("Data will be updated in few seconds"),
        )
        onRefresh?.()
      } else {
        throw new Error(t("Cancel failed"))
      }
    } catch (e) {
      showError(t("Cancel failed"))
      logger.error("Cancel failed", e)
    } finally {
      setLoading(false)
    }
  }

  if (!address) {
    return null
  }

  return buttonContent ? (
    <Pressable onPress={handleCancel}>{buttonContent}</Pressable>
  ) : (
    <PrimaryButton
      title={t("Cancel")}
      contentColor={Colors.PalleteWhite}
      onPress={handleCancel}
      color={Colors.Danger500}
    />
  )
}

export { CancelLoanButton }
