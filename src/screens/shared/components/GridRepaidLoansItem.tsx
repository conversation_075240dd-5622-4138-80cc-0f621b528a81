import React from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { GridContentLoanView } from "src/screens/shared/components"
import { MortgageLoan } from "@/service/types"
import { useTranslation } from "react-i18next"
import { CustomPressable } from "@/components"
import * as Routes from "src/navigation/Routers"
import { useAppNavigaton } from "src/navigation/Routers"

interface GridRepaidLoansItemProps {
  item: MortgageLoan
  style?: ViewStyle
}

const GridRepaidLoansItem: React.FC<GridRepaidLoansItemProps> = ({ item, style }) => {
  const { t } = useTranslation()
  const {
    id,
    dueInSeconds,
    mortgageAmount,
    currency,
    principal,
    repayment,
    apr,
    estate: {
      decimals,
      metadata: {
        imageUrl,
        metadata: {
          name,
          locale_detail: { zone },
        },
      },
    },
  } = item

  const buttonRepaidText = t("Repaid")
  const renderRepaidButton = () => (
    <CustomPressable style={styles.repaidButton} onPress={handleNavigateDetail}>
      <Text style={styles.repaidButtonText}>{buttonRepaidText}</Text>
    </CustomPressable>
  )

  const navigation = useAppNavigaton()

  const handleNavigateDetail = () => {
    navigation.navigate(Routes.LOAN_DETAIL, { id })
  }

  return (
    <View style={style}>
      <GridContentLoanView
        onPress={handleNavigateDetail}
        buttonView={renderRepaidButton()}
        dueInSeconds={dueInSeconds}
        mortgageAmount={mortgageAmount}
        currency={currency}
        principal={principal}
        repayment={repayment}
        apr={apr}
        decimals={decimals}
        imageUrl={imageUrl}
        name={name}
        zone={zone}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  repaidButton: {
    borderRadius: 4,
    margin: 6,
    paddingHorizontal: 8,
    paddingVertical: 6,
    alignItems: "center",
    backgroundColor: Colors.Neutral700,
  },
  repaidButtonText: {
    ...textStyles.MMedium,
    color: Colors.Neutral900,
  },
})

export default GridRepaidLoansItem
