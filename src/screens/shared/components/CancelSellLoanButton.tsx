import React from "react"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { Offer } from "src/service/types"
import { mortgageMarketplaceAbi } from "@/contracts/mortgage-marketplace"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE } from "src/config/env"
import { showError, showSuccessWhenCallContract } from "@/utils/toast"
import Logger from "src/utils/logger"
import useLoadingStore from "@/stores/loadingStore"
import { Pressable } from "react-native"
import { PrimaryButton } from "@/components/Button"
import Colors from "@/config/colors"

interface CancelSellLoanButtonProps {
  offer: Offer
  buttonContent: React.ReactNode
  onRefresh?: () => void
}

const logger = new Logger({ tag: "CancelSellLoanButton" })

export const CancelSellLoanButton: React.FC<CancelSellLoanButtonProps> = ({
  offer,
  buttonContent,
  onRefresh,
}) => {
  const { t } = useTranslation()
  const { isLoading, setLoading } = useLoadingStore()
  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const handleCancel = async () => {
    if (isLoading || !ethersProvider || !CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE) return
    setLoading(true)
    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE,
        abi: mortgageMarketplaceAbi,
        functionName: "cancel",
        args: [BigInt(offer.id)],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Cancellation success") + ". " + t("Data will be updated in few seconds"),
        )
        onRefresh?.()
      } else {
        showError(t("Cancellation failed"))
      }
    } catch (e: any) {
      showError(t("Cancellation failed"))
      logger.error("Cancel sell listing failed", e)
    } finally {
      setLoading(false)
    }
  }

  if (
    !address ||
    !CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE ||
    address.toLowerCase() !== offer.sellerAddress.toLowerCase()
  ) {
    return null
  }

  return buttonContent ? (
    <Pressable onPress={handleCancel}>{buttonContent}</Pressable>
  ) : (
    <PrimaryButton title={t("Cancel")} onPress={handleCancel} color={Colors.Danger500} />
  )
}
