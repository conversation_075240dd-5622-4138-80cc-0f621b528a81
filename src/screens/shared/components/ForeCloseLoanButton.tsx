import React from "react"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { MortgageLoan } from "src/service/types"
import { mortgageTokenAbi } from "src/contracts/mortgage-token"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { CONTRACT_ADDRESS_MORTGAGE_TOKEN } from "src/config/env"
import { showError, showSuccessWhenCallContract } from "@/utils/toast"
import Logger from "src/utils/logger"
import useLoadingStore from "@/stores/loadingStore"
import { Pressable } from "react-native"
import { PrimaryButton } from "@/components/Button"
import Colors from "@/config/colors"

interface ForeCloseLoanButtonProps {
  loan: MortgageLoan
  buttonContent?: React.ReactNode
  onRefresh?: () => void
}

const logger = new Logger({ tag: "ForeCloseLoanButton" })

const ForeCloseLoanButton: React.FC<ForeCloseLoanButtonProps> = ({
  loan,
  buttonContent,
  onRefresh,
}) => {
  const { t } = useTranslation()
  const { isLoading, setLoading } = useLoadingStore()
  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const handleForeclose = async () => {
    if (isLoading || !ethersProvider) return
    setLoading(true)
    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
        abi: mortgageTokenAbi,
        functionName: "foreclose",
        args: [BigInt(loan.id)],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Foreclose success") + ". " + t("Data will be updated in few seconds"),
        )
        onRefresh?.()
      } else {
        showError(t("Foreclose failed"))
      }
    } catch (e: any) {
      showError(t("Foreclose failed"))
      logger.error("Foreclose failed", e)
    } finally {
      setLoading(false)
    }
  }

  if (!address) {
    return null
  }

  return buttonContent ? (
    <Pressable onPress={handleForeclose}>{buttonContent}</Pressable>
  ) : (
    <PrimaryButton title={t("Foreclose")} onPress={handleForeclose} color={Colors.Danger500} />
  )
}

export { ForeCloseLoanButton }
