import { Offer, OfferState } from "@/service/types"
import useAuthStore from "@/stores/authStore"
import React from "react"
import { BuySellLoanButton } from "./BuySellLoanButton"
import { CancelSellLoanButton } from "./CancelSellLoanButton"

interface OfferActionButtonProps {
  offer: Offer
  buyButtonContent?: React.ReactNode
  cancelButtonContent?: React.ReactNode
  onRefresh?: () => void
}

const OfferActionButton: React.FC<OfferActionButtonProps> = ({
  offer,
  buyButtonContent,
  cancelButtonContent,
  onRefresh,
}) => {
  const { address } = useAuthStore()
  const { state, sellerAddress } = offer
  const showBuyBtn =
    state === OfferState.SELLING && address?.toLowerCase() !== sellerAddress?.toLocaleLowerCase()
  const showCancelBtn =
    state === OfferState.SELLING && address?.toLowerCase() === sellerAddress?.toLocaleLowerCase()
  return (
    <>
      {showBuyBtn && (
        <BuySellLoanButton offer={offer} buttonContent={buyButtonContent} onRefresh={onRefresh} />
      )}
      {showCancelBtn && (
        <CancelSellLoanButton
          offer={offer}
          buttonContent={cancelButtonContent}
          onRefresh={onRefresh}
        />
      )}
    </>
  )
}

export default OfferActionButton
