import React from "react"
import {
  StyleSheet,
  Text,
  View,
  Image,
  ImageSourcePropType,
  ViewStyle,
  StyleProp,
} from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { TFunction } from "i18next"

import icCoins from "assets/images/ic_coins.png"
import icTimeOff from "assets/images/ic_timer_off.png"
import icCircleCheck from "assets/images/ic_circle_check.png"
import icCircleX from "assets/images/ic_circle_x.png"

interface LoanStateConfig {
  color: string
  label: string
  textColor: string
  borderColor: string
  icon: ImageSourcePropType | undefined
}

interface LoanStateProps {
  state: string
  style?: StyleProp<ViewStyle>
}

const getLoanStateConfig = (state: string, t: TFunction): LoanStateConfig => {
  const configs: Record<string, LoanStateConfig> = {
    PENDING: {
      color: Colors.Success900,
      borderColor: Colors.Success800,
      icon: undefined,
      label: t("Open"),
      textColor: Colors.PalleteWhite,
    },
    SUPPLIED: {
      color: Colors.Success500,
      borderColor: Colors.Success800,
      icon: icCoins,
      label: t("Lent"),
      textColor: Colors.PalleteWhite,
    },
    OVERDUE: {
      color: Colors.Danger900,
      borderColor: Colors.Danger800,
      icon: icTimeOff,
      label: t("Ovedue"),
      textColor: Colors.Danger500,
    },
    REPAID: {
      color: Colors.Success500,
      borderColor: Colors.Success800,
      icon: icCircleCheck,
      label: t("Repaid"),
      textColor: Colors.PalleteWhite,
    },
    FORECLOSED: {
      color: Colors.Danger900,
      borderColor: Colors.Danger800,
      icon: icCoins,
      label: t("Foreclosed"),
      textColor: Colors.PalleteWhite,
    },
    CANCELLED: {
      color: Colors.Danger500,
      borderColor: Colors.Danger500,
      icon: icCircleX,
      label: t("Canceled"),
      textColor: Colors.PalleteWhite,
    },
  }

  return (
    configs[state] || {
      color: Colors.Danger500,
      borderColor: Colors.Danger500,
      label: t("Unknown status"),
      icon: icCircleX,
      textColor: Colors.PalleteWhite,
    }
  )
}

const LoanState: React.FC<LoanStateProps> = ({ state, style }) => {
  const { t } = useTranslation()
  const config = getLoanStateConfig(state, t)

  return (
    <View
      style={[
        styles.container,
        style,
        { borderColor: config.borderColor, backgroundColor: config.color },
      ]}
    >
      {config.icon ? (
        <Image source={config.icon} style={viewStyles.size8Icon} />
      ) : (
        <View style={styles.circleSuccess} />
      )}
      <Text style={[styles.status, { color: config.textColor }]}>{config.label}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 4,
    borderRadius: 999,
    borderWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "flex-start",
    height: 16,
  },
  status: {
    ...textStyles.MSemiBold,
    paddingStart: 4,
  },
  circleSuccess: {
    backgroundColor: Colors.Success500,
    borderRadius: 999,
    width: 8,
    height: 8,
  },
})

export default LoanState
