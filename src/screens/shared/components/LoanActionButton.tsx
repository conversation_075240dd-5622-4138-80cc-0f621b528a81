import React from "react"
import { MortgageLoan, MortgageLoanState } from "src/service/types"
import { CancelLoanButton } from "./CancelLoanButton"
import { ForeCloseLoanButton } from "./ForeCloseLoanButton"
import { LendLoanButton } from "./LendLoanButton"
import { RepayLoanButton } from "./RepayLoanButton"
import { SellLoanButton } from "./SellLoanButton"
import useAuthStore from "@/stores/authStore"

interface LoanActionButtonProps {
  loan: MortgageLoan
  foreCloseButtonContent?: React.ReactNode
  lendButtonContent?: React.ReactNode
  cancelButtonContent?: React.ReactNode
  repayButtonContent?: React.ReactNode
  sellLoanButtonContent?: React.ReactNode
  onRefresh?: () => void
}

export const LoanActionButton: React.FC<LoanActionButtonProps> = ({
  loan,
  onRefresh,
  foreCloseButtonContent,
  lendButtonContent,
  cancelButtonContent,
  repayButtonContent,
  sellLoanButtonContent,
}) => {
  const { address } = useAuthStore()
  const { state, lender, borrower, dueInSeconds } = loan

  const overdue =
    state !== MortgageLoanState.PENDING &&
    state !== MortgageLoanState.CANCELLED &&
    new Date().getTime() / 1000 > dueInSeconds

  const showLendBtn =
    state === MortgageLoanState.PENDING && address?.toLowerCase() !== borrower.address
  const showCancelBtn =
    state === MortgageLoanState.PENDING && address?.toLowerCase() === borrower.address
  const showRepayBtn =
    state === MortgageLoanState.SUPPLIED && address?.toLowerCase() === borrower.address
  const showForecloseBtn =
    state === MortgageLoanState.OVERDUE && overdue && address?.toLowerCase() === lender.address
  const showSellBtn =
    state === MortgageLoanState.SUPPLIED && !overdue && address?.toLowerCase() === lender.address

  return (
    <>
      {showForecloseBtn && (
        <ForeCloseLoanButton
          loan={loan}
          buttonContent={foreCloseButtonContent}
          onRefresh={onRefresh}
        />
      )}
      {showLendBtn && (
        <LendLoanButton loan={loan} buttonContent={lendButtonContent} onRefresh={onRefresh} />
      )}
      {showCancelBtn && (
        <CancelLoanButton loan={loan} buttonContent={cancelButtonContent} onRefresh={onRefresh} />
      )}
      {showRepayBtn && (
        <RepayLoanButton loan={loan} buttonContent={repayButtonContent} onRefresh={onRefresh} />
      )}
      {showSellBtn && (
        <SellLoanButton loan={loan} onRefresh={onRefresh} buttonContent={sellLoanButtonContent} />
      )}
    </>
  )
}
