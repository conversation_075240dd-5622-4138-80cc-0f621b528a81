import React from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { GridContentLoanView } from "src/screens/shared/components"
import { Offer } from "@/service/types"
import { useTranslation } from "react-i18next"
import { useCurrencies } from "@/screens/shared/hooks/useCurrencies"
import { formatCurrencyByDecimals } from "@/utils/format"
import OfferActionButton from "./OfferActionButton"

interface GridLoanSaleMarketplaceItemProps {
  item: Offer
  style?: ViewStyle
}

const GridLoanSaleMarketplaceItem: React.FC<GridLoanSaleMarketplaceItemProps> = ({
  item,
  style,
}) => {
  const { t } = useTranslation()
  const {
    price,
    due,
    mortgageLoan: { mortgageAmount, currency, principal, repayment, apr, lender },
    estate: {
      decimals,
      metadata: {
        imageUrl,
        metadata: {
          name,
          locale_detail: { zone },
        },
      },
    },
  } = item

  const { tokenSymbol } = useCurrencies(currency)

  const renderBuyButton = () => (
    <View style={styles.buyButton}>
      <Text
        style={styles.buyButtonText}
      >{`${t("Buy Loan")} ${formatCurrencyByDecimals(price, decimals)} ${tokenSymbol}`}</Text>
    </View>
  )

  const renderCancelButton = () => (
    <View style={styles.cancelButton}>
      <Text style={textStyles.MMedium}>{t("Cancel")}</Text>
    </View>
  )

  return (
    <View style={style}>
      <GridContentLoanView
        buttonView={
          <OfferActionButton
            offer={item}
            buyButtonContent={renderBuyButton()}
            cancelButtonContent={renderCancelButton()}
          />
        }
        dueInSeconds={due}
        mortgageAmount={mortgageAmount}
        currency={currency}
        principal={principal}
        repayment={repayment}
        apr={apr}
        decimals={decimals}
        imageUrl={imageUrl}
        name={name}
        zone={zone}
        lender={lender}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  buyButton: {
    borderRadius: 4,
    margin: 6,
    paddingHorizontal: 8,
    paddingVertical: 6,
    alignItems: "center",
    backgroundColor: Colors.Success300,
  },
  buyButtonText: {
    ...textStyles.MMedium,
    color: Colors.PalleteBlack,
  },
  cancelButton: {
    borderRadius: 4,
    margin: 6,
    paddingHorizontal: 8,
    paddingVertical: 6,
    alignItems: "center",
    backgroundColor: Colors.Danger500,
  },
})

export default GridLoanSaleMarketplaceItem
