import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { CustomPressable } from "@/components"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"

interface TabSelectorProps {
  tabTitles: string[]
  selectedIndex: number
  setTabIndex: (index: number) => void
}

const TabSelector: React.FC<TabSelectorProps> = ({ tabTitles, selectedIndex, setTabIndex }) => {
  return (
    <View style={styles.tabContainer}>
      {tabTitles.map((title, index) => (
        <CustomPressable key={index} style={styles.tabButton} onPress={() => setTabIndex(index)}>
          <Text style={[styles.tabText, selectedIndex === index && styles.selectedTabText]}>
            {title}
          </Text>
          {selectedIndex === index && <View style={styles.activeIndicator} />}
        </CustomPressable>
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: Colors.Neutral950,
    paddingRight: 12,
  },
  tabButton: {
    paddingVertical: 12,
    position: "relative",
    marginRight: 12,
  },
  tabText: {
    ...textStyles.LMedium,
    color: Colors.Neutral500,
    textAlign: "center",
  },
  selectedTabText: {
    color: Colors.PalleteWhite,
  },
  activeIndicator: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: Colors.PalleteWhite,
  },
})

export default TabSelector
