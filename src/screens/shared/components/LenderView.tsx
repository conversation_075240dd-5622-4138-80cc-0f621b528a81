import React from "react"
import { Image, StyleSheet, Text, View, StyleProp, ViewStyle } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import { PublicUser } from "@/service/types"
import { useTranslation } from "react-i18next"
import { shortenAddress } from "@/utils"

interface LenderViewProps {
  lender: PublicUser
  style?: StyleProp<ViewStyle>
}

const LenderView: React.FC<LenderViewProps> = ({ lender, style }) => {
  const { t } = useTranslation()

  const { address, avatarUrl } = lender
  const shortAddress = shortenAddress(address)

  return (
    <View style={[styles.container, style]}>
      <Text style={textStyles.XSMedium}>{t("Lender")}</Text>
      {avatarUrl && <Image source={{ uri: avatarUrl }} style={styles.icon} />}
      <Text style={textStyles.XSMedium}>{shortAddress}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
  },
  icon: {
    ...viewStyles.size8Icon,
    borderRadius: 999,
  },
})

export default LenderView
