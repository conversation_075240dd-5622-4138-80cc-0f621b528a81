import { useSuspenseQuery } from "@tanstack/react-query"
import { getCurrencies } from "@/service"
import { QueryKeys } from "@/config/queryKeys"
import { queryOptions } from "@tanstack/react-query"

const getCurrenciesQueryOptions = queryOptions({
  queryKey: [QueryKeys.CURRENCY.LIST],
  queryFn: getCurrencies,
})

export function useCurrencies(currency: string) {
  const { data: currencies = [] } = useSuspenseQuery(getCurrenciesQueryOptions)

  const tokenData = currency ? currencies.find((c) => c.currency === currency) : undefined
  const tokenSymbol = tokenData?.symbol ?? ""
  const tokenImageUrl = tokenData?.imageUrl ?? ""

  return {
    tokenSymbol,
    tokenImageUrl,
  }
}
