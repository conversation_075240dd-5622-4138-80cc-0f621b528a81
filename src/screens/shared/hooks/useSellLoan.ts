import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useTranslation } from "react-i18next"
import { CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE } from "src/config/env"
import { showError, showSuccessWhenCallContract } from "@/utils/toast"
import { mortgageMarketplaceAbi } from "@/contracts/mortgage-marketplace"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { useWriteContract, useChainId } from "wagmi"
import Logger from "src/utils/logger"
import { MortgageLoan } from "@/service/types"
import { formatCurrencyByDecimals } from "@/utils"
import { useCurrencies } from "./useCurrencies"

const logger = new Logger({ tag: "useSellLoan" })

const useFormSchema = () => {
  const { t } = useTranslation()

  return z.object({
    sellPrice: z.string().min(1, {
      message: t("Please input a valid sell price greater than 0"),
    }),
  })
}

export const useSellLoan = (loan: MortgageLoan, onRefresh?: () => void) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const chainId = useChainId()

  const {
    estate: { decimals },
    principal,
    mortgageAmount,
    currency,
  } = loan

  const formatedPrincipal = formatCurrencyByDecimals(principal, decimals)
  const formattedMortgageAmount = formatCurrencyByDecimals(mortgageAmount, decimals)
  const { tokenSymbol, tokenImageUrl } = useCurrencies(currency)

  const formSchema = useFormSchema()
  type Payload = z.infer<typeof formSchema>
  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sellPrice: "",
    },
  })

  const chainNamesMap: Record<number, string> = {
    56: t("Binance Smart Chain"),
    97: t("Binance Smart Chain Testnet"),
  }

  const chainName = chainNamesMap[chainId] || t("Unknown Chain")
  const sellPrice = form.watch("sellPrice")
  const isSellingPositive = Number(sellPrice) > 0

  const onSubmit = async (data: Payload) => {
    if (!isLoading || !ethersProvider || !CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE) return
    setIsLoading(true)
    try {
      const pow10 = BigInt(Math.pow(10, decimals))
      const priceValue = BigInt(data.sellPrice) * pow10
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE,
        abi: mortgageMarketplaceAbi,
        functionName: "list",
        args: [BigInt(loan.id), priceValue, currency],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Listing success") + ". " + t("Data will be updated in few seconds"),
        )
        onRefresh?.()
      } else {
        showError(t("Listing failed"))
      }
    } catch (e: any) {
      showError(t("Listing failed"))
      logger.error("List for sale failed", e)
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    form.reset()
    setIsLoading(false)
  }

  return {
    isLoading,
    form,
    formatedPrincipal,
    formattedMortgageAmount,
    tokenSymbol,
    tokenImageUrl,
    chainName,
    sellPrice,
    isSellingPositive,
    onSubmit,
    resetForm,
  }
}
