import { useMemo } from "react"
import { MortgageLoan } from "@/service/types"
import { LoanDetailSectionType, LoanDetailSectionItem } from "../types/sections"

export const useLoanSections = (loan: MortgageLoan | undefined) => {
  const sections = useMemo(() => {
    if (!loan) return []

    const result: LoanDetailSectionItem[] = []

    // Basic section
    result.push({
      type: LoanDetailSectionType.BASIC,
      id: `${LoanDetailSectionType.BASIC}-${loan.id}`,
      loan: loan,
    })

    // Loan attributes section
    result.push({
      type: LoanDetailSectionType.LOAN_ATTRIBUTES,
      id: `${LoanDetailSectionType.LOAN_ATTRIBUTES}-${loan.id}`,
      loan: loan,
    })

    // Loan action buttons section
    result.push({
      type: LoanDetailSectionType.LOAN_ACTION_BUTTONS,
      id: `${LoanDetailSectionType.LOAN_ACTION_BUTTONS}-${loan.id}`,
      loan: loan,
    })

    // Loan tabs section
    result.push({
      type: LoanDetailSectionType.LOAN_TABS,
      id: `${LoanDetailSectionType.LOAN_TABS}-${loan.id}`,
    })

    return result
  }, [loan])

  return {
    sections,
  }
}
