import { useEffect, useState } from "react"
import { LoanDetailContextState } from "../context/types"
import { useLoanDetail } from "./useLoanDetail"
import useLoadingStore from "@/stores/loadingStore"
import { useLoanSections } from "./useLoanSections"
import { useTranslation } from "react-i18next"
import { MortgageLoanState } from "@/service/types"

/**
 * and provides the context value
 * @param loanId The ID of the loan to fetch data for
 * @param isFocused Whether the screen is currently focused
 */
export const useLoanDetailProvider = (
  id: string,
  isFocused: boolean = true,
): LoanDetailContextState => {
  const { setLoading } = useLoadingStore()
  const { t } = useTranslation()
  const {
    data: loan,
    isLoading: isLoadingLoanDetail,
    error: LoanDetailError,
    refetch: refetchLoanDetail,
  } = useLoanDetail(id, isFocused)

  useEffect(() => {
    // delay 100ms
    const delay = setTimeout(() => {
      if (isLoadingLoanDetail) {
        setLoading(true)
      }
    }, 0)
    return () => {
      clearTimeout(delay)
      setLoading(false)
    }
  }, [isLoadingLoanDetail, setLoading])

  const { sections } = useLoanSections(loan)

  const [activeTabIndex, setActiveTabIndex] = useState(0)
  const tabTitles = [t("Open"), t("Disbursed"), t("Other")]
  const openTabStates = [MortgageLoanState.PENDING].join(",")
  const disbursedTabStates = [MortgageLoanState.SUPPLIED].join(",")
  const otherTabStates = [
    MortgageLoanState.OVERDUE,
    MortgageLoanState.REPAID,
    MortgageLoanState.CANCELLED,
  ].join(",")

  return {
    sections,
    loanId: id,
    loan: loan || null,
    loanDetailError: LoanDetailError as Error | null,
    isLoadingLoanDetail,
    refreshLoanDetail: async () => {
      await refetchLoanDetail()
    },
    openTabStates,
    disbursedTabStates,
    otherTabStates,
    tabTitles,
    activeTabIndex,
    setActiveTabIndex,
  }
}
