import { useQueryWithErrorHandling } from "src/config/queryClient"
import QueryKeys from "src/config/queryKeys"
import { getLoanDetailByIdApi } from "src/service/index"
import { MortgageLoan } from "@/service/types"

export const useLoanDetail = (id: string, isFocused: boolean = true) => {
  return useQueryWithErrorHandling<MortgageLoan>({
    queryKey: QueryKeys.LOAN.DETAIL(id || ""),
    queryFn: () => getLoanDetailByIdApi(id || ""),
    refetchInterval: isFocused ? 10_000 : false,
    refetchIntervalInBackground: false,
    enabled: isFocused,
  })
}
