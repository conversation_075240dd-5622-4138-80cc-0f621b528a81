import * as React from "react"
import LoanDetailView from "./LoanDetailView"
import { useIsFocused, useRoute } from "@react-navigation/native"
import { RouteProp } from "@react-navigation/native"
import { LoanDetailProvider } from "./context/LoanDetailContext"
import { RootStackParamList, LOAN_DETAIL } from "@/navigation/Routers"

const LoanDetailScreen = () => {
  const route = useRoute<RouteProp<RootStackParamList, typeof LOAN_DETAIL>>()
  const { id } = route.params
  const isFocused = useIsFocused()

  if (id === undefined || id === "") {
    return null
  }

  return (
    <LoanDetailProvider loanId={id} isFocused={isFocused}>
      <LoanDetailView />
    </LoanDetailProvider>
  )
}

export default LoanDetailScreen
