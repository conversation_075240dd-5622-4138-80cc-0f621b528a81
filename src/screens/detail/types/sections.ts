import { MortgageLoan } from "@/service/types"

export enum LoanDetailSectionType {
  BASIC = "basic",
  LOAN_TABS = "loanTabs",
  LOAN_ATTRIBUTES = "loan_attributes",
  LOAN_ACTION_BUTTONS = "loan_action_buttons",
}

/**
 * Base interface for all section items
 */
export interface BaseSectionItem {
  type: LoanDetailSectionType
  id: string
}

export interface BasicSectionItem extends BaseSectionItem {
  type: LoanDetailSectionType.BASIC
  loan: MortgageLoan
}

export interface LoanAttributesSectionItem extends BaseSectionItem {
  type: LoanDetailSectionType.LOAN_ATTRIBUTES
  loan: MortgageLoan
}

export interface LoanActionButtonsSectionItem extends BaseSectionItem {
  type: LoanDetailSectionType.LOAN_ACTION_BUTTONS
  loan: MortgageLoan
}

export interface LoanTabsSectionItem extends BaseSectionItem {
  type: LoanDetailSectionType.LOAN_TABS
}

export type LoanDetailSectionItem =
  | LoanTabsSectionItem
  | BasicSectionItem
  | LoanAttributesSectionItem
  | LoanActionButtonsSectionItem
