import { MortgageLoan } from "@/service/types"
import { LoanDetailSectionItem } from "src/screens/detail/types"
import { Dispatch, SetStateAction } from "react"

export interface LoanDetailContextState {
  loanId: string
  sections: LoanDetailSectionItem[]
  loan: MortgageLoan | null
  loanDetailError: Error | null
  isLoadingLoanDetail: boolean
  refreshLoanDetail: () => Promise<void>
  tabTitles: string[]
  openTabStates: string
  disbursedTabStates: string
  otherTabStates: string
  activeTabIndex: number
  setActiveTabIndex: Dispatch<SetStateAction<number>>
}
