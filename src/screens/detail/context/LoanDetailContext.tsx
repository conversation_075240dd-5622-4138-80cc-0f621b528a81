import React, { createContext, useContext, ReactNode } from "react"
import { useLoanDetailProvider } from "../hooks/useLoanDetailProvider"
import { MortgageLoan } from "@/service/types"
import { LoanDetailContextState } from "./types"

export const LoanDetailContext = createContext<LoanDetailContextState>({
  loanId: "",
  loan: null,
  loanDetailError: null,
  sections: [],
  isLoadingLoanDetail: false,
  refreshLoanDetail: async () => {},
  openTabStates: "",
  disbursedTabStates: "",
  otherTabStates: "",
  tabTitles: [],
  activeTabIndex: 0,
  setActiveTabIndex: () => {},
})

export const useLoanDetailContext = () => {
  return useContext(LoanDetailContext)
}

interface LoanDetailProviderProps {
  children: ReactNode
  loanId: string
  isFocused?: boolean
  initialLoanDetail?: MortgageLoan | null
}

export const LoanDetailProvider: React.FC<LoanDetailProviderProps> = ({
  children,
  loanId,
  isFocused = true,
}) => {
  const contextValue = useLoanDetailProvider(loanId, isFocused)

  return <LoanDetailContext.Provider value={contextValue}>{children}</LoanDetailContext.Provider>
}
