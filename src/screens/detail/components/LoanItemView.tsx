import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "@/config/colors"
import { textStyles, viewStyles } from "@/config/styles"
import { MortgageLoan, MortgageLoanState } from "@/service/types"
import { useCurrencies } from "@/screens/shared/hooks/useCurrencies"
import iconClock9 from "assets/images/ic_clock_9.png"
import { formatCurrencyByDecimals, shortenAddress } from "@/utils"
import { LoanStateView } from "@/screens/shared/components"
import { convertSecondsToTime } from "@/utils/timeExt"
import { AvatarView } from "@/components/AvatarView"

interface UserViewProps {
  label: string
  avatarUrl: string
  address: string
}

const UserView: React.FC<UserViewProps> = ({ label, avatarUrl, address }) => {
  return (
    <View style={styles.row}>
      <Text style={styles.label}>{label}</Text>
      {avatarUrl && <AvatarView avatarUrl={avatarUrl} size={10} style={styles.avatar} />}
      <Text style={styles.address}>{shortenAddress(address)}</Text>
    </View>
  )
}

interface LoanItemViewProps {
  item: MortgageLoan
  actionButton: React.ReactNode
}

const LoanItemView: React.FC<LoanItemViewProps> = ({ item, actionButton }) => {
  const { t } = useTranslation()
  const {
    state,
    currency,
    principal,
    repayment,
    mortgageAmount,
    durationInSeconds,
    estate: { decimals },
    apr,
    borrower: { address: borrowerAddres, avatarUrl: borrowerAvatar },
    lender: { address: lenderAddres, avatarUrl: lenderAvatar },
  } = item
  const formatedPrincipal = formatCurrencyByDecimals(principal, decimals)
  const formatedRepayment = formatCurrencyByDecimals(repayment, decimals)
  const formattedMortgageAmount = formatCurrencyByDecimals(mortgageAmount, decimals)
  const { tokenImageUrl } = useCurrencies(currency)

  return (
    <View style={styles.cardContainer}>
      <View style={styles.rowSpaceBetween}>
        <LoanStateView state={state} />
        {state === MortgageLoanState.PENDING ? (
          <UserView label={t("Borrower")} avatarUrl={borrowerAvatar} address={borrowerAddres} />
        ) : (
          <View style={styles.rowBetween}>
            <UserView label={t("Borrower")} avatarUrl={borrowerAvatar} address={borrowerAddres} />
            <UserView label={t("Lender")} avatarUrl={lenderAvatar} address={lenderAddres} />
          </View>
        )}
      </View>

      <View style={styles.detailsRow}>
        <View style={styles.detailItemContainer}>
          <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Principal")}</Text>
          <View style={styles.amountContainer}>
            <Text style={textStyles.SSemiBold}>{formatedPrincipal}</Text>
            <Image source={{ uri: tokenImageUrl }} style={viewStyles.size8Icon} />
          </View>
        </View>
        <View style={styles.detailItemContainer}>
          <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Repayment")}</Text>
          <View style={styles.amountContainer}>
            <Text style={textStyles.SSemiBold}>{formatedRepayment}</Text>
            <Image source={{ uri: tokenImageUrl }} style={viewStyles.size8Icon} />
          </View>
        </View>
      </View>
      <View style={styles.detailsSection}>
        <View style={styles.detailsRow}>
          <View style={styles.detailItemContainer}>
            <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("APR")}</Text>

            <Text style={textStyles.SMedium}>{`${apr}%`}</Text>
          </View>
          <View style={styles.detailItemContainer}>
            <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Collateral")}</Text>
            <Text
              style={[textStyles.SRegular, styles.detailItemValueRight]}
            >{`${formattedMortgageAmount} ${t("NFTs")}`}</Text>
          </View>
        </View>
      </View>
      <View style={styles.rowSpaceBetween}>
        <View style={styles.row}>
          <Image source={iconClock9} style={viewStyles.size12Icon} />
          <Text style={styles.duration}>{t("Duration")}</Text>
          <Text style={textStyles.SMedium}>{convertSecondsToTime(durationInSeconds)}</Text>
        </View>
        <View style={styles.actionButton}>{actionButton}</View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  cardContainer: {
    flexDirection: "column",
    alignItems: "center",
    paddingVertical: 16,
    gap: 12,
    width: "100%",
    borderBottomWidth: 1,
    borderBottomColor: Colors.Neutral950,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "space-between",
    alignItems: "center",
  },
  detailsSection: {
    flexDirection: "column",
    alignItems: "flex-start",
    gap: 6,
    width: "100%",
  },
  detailsRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    width: "100%",
  },
  detailItemContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 8,
    gap: 4,
    height: 28,
    borderWidth: 1,
    borderColor: Colors.Neutral950,
    borderRadius: 4,
  },
  detailItemLabel: {
    color: Colors.Neutral500,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailItemValueRight: {
    color: Colors.PalleteWhite,
    textAlign: "right",
    flexShrink: 1,
  },
  amountContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  actionButton: {
    alignSelf: "flex-end",
  },
  duration: {
    ...textStyles.SMedium,
    color: Colors.Neutral500,
    marginHorizontal: 4,
  },
  label: {
    ...textStyles.SMedium,
    color: Colors.Neutral500,
  },
  address: {
    ...textStyles.SMedium,
    color: Colors.Secondary300,
    marginStart: 4,
  },
  rowBetween: {
    flex: 1,
    marginStart: 8,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  avatar: {
    marginStart: 4,
  },
})

export default LoanItemView
