import React from "react"
import { View, StyleSheet } from "react-native"
import { TabSelector } from "@/screens/shared/components"
import Colors from "@/config/colors"
import OpenLoansTab from "./OpenLoansTab"
import OtherLoansTab from "./OtherLoansTab"
import DisbursedLoansTab from "./DisbursedLoansTab"
import { useLoanDetailContext } from "src/screens/detail/context/LoanDetailContext"

const LoanTabs: React.FC = () => {
  const { activeTabIndex, setActiveTabIndex, tabTitles } = useLoanDetailContext()

  return (
    <View>
      <TabSelector
        tabTitles={tabTitles}
        selectedIndex={activeTabIndex}
        setTabIndex={setActiveTabIndex}
      />
      <View style={styles.tabContent}>
        {activeTabIndex === 0 && <OpenLoansTab />}
        {activeTabIndex === 1 && <DisbursedLoansTab />}
        {activeTabIndex === 2 && <OtherLoansTab />}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  tabSelectorContainer: {
    paddingHorizontal: 16,
    backgroundColor: Colors.PalleteBlack,
  },
  tabContent: {
    flex: 1,
    backgroundColor: Colors.PalleteBlack,
    paddingTop: 16,
    paddingBottom: 20,
  },
})

export default LoanTabs
