import React from "react"
import { LoanAttributesSectionItem } from "src/screens/detail/types"
import LoanAttributesView from "../LoanAttributesView"

interface LoanAttributesSectionProps {
  item: LoanAttributesSectionItem
}

const LoanAttributesSection: React.FC<LoanAttributesSectionProps> = ({ item }) => {
  const loan = item.loan
  const {
    currency,
    principal,
    repayment,
    mortgageAmount,
    durationInSeconds,
    dueInSeconds,
    estate: { decimals },
    apr,
  } = loan

  return (
    <LoanAttributesView
      currency={currency}
      mortgageAmount={mortgageAmount}
      principal={principal}
      apr={apr}
      repayment={repayment}
      durationInSeconds={durationInSeconds}
      dueInSeconds={dueInSeconds}
      decimals={decimals}
    />
  )
}

export default LoanAttributesSection
