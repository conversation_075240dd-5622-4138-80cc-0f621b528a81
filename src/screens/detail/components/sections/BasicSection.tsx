import React from "react"
import { BasicSectionItem } from "src/screens/detail/types"
import BasicSectionLoanView from "../BasicSectionLoanView"

interface BasicSectionProps {
  item: BasicSectionItem
}

const BasicSection: React.FC<BasicSectionProps> = ({ item }) => {
  const { loan } = item

  const {
    estate: {
      id,
      metadata: {
        metadata: { address, image, name, locale_detail },
      },
      createAtInSeconds,
    },
    borrower: { address: borrowAddress, avatarUrl },
  } = loan
  return (
    <BasicSectionLoanView
      estateId={id}
      image={image}
      name={name}
      createdAt={createAtInSeconds}
      address={address}
      borrowerAvatar={avatarUrl}
      borrowerAddress={borrowAddress}
      locale_detail={locale_detail}
    />
  )
}

export default BasicSection
