import React from "react"
import { LoanActionButtonsSectionItem } from "src/screens/detail/types"
import LoanActionButtonsView from "../LoanActionButtonsView"

interface LoanActionButtonsSectionProps {
  item: LoanActionButtonsSectionItem
}

const LoanActionButtonsSection: React.FC<LoanActionButtonsSectionProps> = ({ item }) => {
  const { loan } = item

  return <LoanActionButtonsView loan={loan} />
}

export default LoanActionButtonsSection
