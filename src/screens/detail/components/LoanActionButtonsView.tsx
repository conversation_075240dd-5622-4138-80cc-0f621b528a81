import React from "react"
import { View, StyleSheet, Text, Image } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "@/config/colors"
import { LoanActionButton } from "@/screens/shared/components/LoanActionButton"
import { MortgageLoan, MortgageLoanState } from "@/service/types"
import { textStyles, viewStyles } from "@/config/styles"
import useWalletConnect from "@/hooks/useWalletConnect"
import useAuthStore from "@/stores/authStore"
import { PrimaryButton } from "@/components/Button"
import { formatCurrencyByDecimals } from "@/utils"
import { useCurrencies } from "@/screens/shared/hooks/useCurrencies"

interface LoanActionButtonsViewProps {
  loan: MortgageLoan
}

interface RepaymentViewProps {
  repaymentAmount: string
  tokenImageUrl: string
}

const RepaymentView: React.FC<RepaymentViewProps> = ({ repaymentAmount, tokenImageUrl }) => {
  const { t } = useTranslation()
  return (
    <View style={styles.repay}>
      <Text style={styles.repayTitle}>{t("Repayment")}</Text>
      <View style={styles.row}>
        <Text style={textStyles.size2XLMedium}>{repaymentAmount}</Text>
        <Image source={{ uri: tokenImageUrl }} style={styles.tokenImage} />
      </View>
    </View>
  )
}

//TODO: View này dùng để wrap LoanAction trong Loan detail, sẽ update tên hợp lý hơn sau
const LoanActionButtonsView: React.FC<LoanActionButtonsViewProps> = ({ loan }) => {
  const { t } = useTranslation()
  const { connect } = useWalletConnect()
  const { isAuthenticated, address } = useAuthStore()

  const {
    mortgageAmount,
    state,
    repayment,
    estate: { decimals },
    borrower,
    currency,
  } = loan

  const formattedMortgageAmount = formatCurrencyByDecimals(mortgageAmount, decimals)
  const formattedRepaymentAmount = formatCurrencyByDecimals(repayment, decimals)
  const { tokenImageUrl } = useCurrencies(currency)

  const showRepaymentView =
    state === MortgageLoanState.REPAID ||
    (state === MortgageLoanState.OVERDUE && address?.toLowerCase() === borrower.address)

  const renderForeCloseButton = () => (
    <Text style={styles.positiveButtonText}>
      {t("Claim NFT", { amount: formattedMortgageAmount })}
    </Text>
  )

  const renderRepayButton = (tokenImageUrl: string) => (
    <View style={styles.repay}>
      <Text style={styles.repayTitle}>{t("Repayment Loan")}</Text>
      <View style={[styles.row, styles.marginBottom10]}>
        <Text style={textStyles.size2XLMedium}>{formattedRepaymentAmount}</Text>
        <Image source={{ uri: tokenImageUrl }} style={styles.tokenImage} />
      </View>
      <Text style={styles.positiveButtonText}>{t("Repay")}</Text>
    </View>
  )

  return (
    <View style={styles.container}>
      {!isAuthenticated ? (
        <PrimaryButton
          title={t("Connect Wallet")}
          onPress={connect}
          height={32}
          textStyle={textStyles.LMedium}
        />
      ) : (
        <>
          {showRepaymentView && (
            <RepaymentView
              repaymentAmount={formattedRepaymentAmount}
              tokenImageUrl={tokenImageUrl}
            />
          )}
          <LoanActionButton
            loan={loan}
            foreCloseButtonContent={renderForeCloseButton()}
            repayButtonContent={renderRepayButton(tokenImageUrl)}
          />
        </>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 12,
  },
  button: {
    flex: 1,
  },
  positiveButtonText: {
    ...textStyles.MMedium,
    borderRadius: 4,
    marginVertical: 6,
    paddingHorizontal: 8,
    paddingVertical: 6,
    alignItems: "center",
    backgroundColor: Colors.Success300,
    color: Colors.PalleteBlack,
    textAlign: "center",
  },
  repay: {
    borderRadius: 6,
    backgroundColor: Colors.Neutral950,
    padding: 16,
  },
  repayTitle: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
    marginBottom: 8,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  marginBottom10: {
    marginBottom: 10,
  },
  tokenImage: {
    ...viewStyles.size24Icon,
    marginStart: 8,
  },
})

export default LoanActionButtonsView
