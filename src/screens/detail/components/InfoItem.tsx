import React from "react"
import { Image, ImageSourcePropType, StyleSheet, Text, View } from "react-native"
import { viewStyles } from "src/config/styles"
import { StyleProp, ImageStyle, TextStyle, ViewStyle } from "react-native"

interface InfoItemProps {
  style?: StyleProp<ViewStyle>
  icon: ImageSourcePropType
  text: string
  iconStyle?: StyleProp<ImageStyle>
  textStyle?: StyleProp<TextStyle>
}

const InfoItem: React.FC<InfoItemProps> = ({ style, icon, text, iconStyle, textStyle }) => {
  return (
    <View style={[styles.infoItem, style]}>
      <Image source={icon} style={[viewStyles.size8Icon, iconStyle]} />
      <Text style={textStyle}>{text}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  infoItem: {
    flexDirection: "row",
  },
})

export default InfoItem
