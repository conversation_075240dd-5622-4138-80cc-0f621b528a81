import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { formatCurrencyByDecimals } from "@/utils"
import { textStyles, viewStyles } from "@/config/styles"
import Colors from "@/config/colors"
import { useTranslation } from "react-i18next"
import { useCurrencies } from "@/screens/shared/hooks/useCurrencies"
import { convertSecondsToTime, formatTimestampToDateTimeString } from "@/utils/timeExt"

interface LoanAttributesViewProps {
  currency: string
  mortgageAmount: string
  principal: string
  apr: number
  repayment: string
  durationInSeconds: number
  dueInSeconds: number
  decimals: number
}

const LoanAttributesView: React.FC<LoanAttributesViewProps> = ({
  currency,
  mortgageAmount,
  principal,
  apr,
  repayment,
  durationInSeconds,
  dueInSeconds,
  decimals,
}) => {
  const { t } = useTranslation()
  const { tokenImageUrl } = useCurrencies(currency)
  const dueDate = formatTimestampToDateTimeString(dueInSeconds, t)
  const formatedPrincipal = formatCurrencyByDecimals(principal, decimals)
  const formatedRepayment = formatCurrencyByDecimals(repayment, decimals)
  const formattedMortgageAmount = formatCurrencyByDecimals(mortgageAmount, decimals)

  return (
    <View style={styles.container}>
      <View style={styles.detailsSection}>
        <View style={styles.detailsRow}>
          <View style={styles.detailItemContainer}>
            <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Collateral")}</Text>
            <Text
              style={[textStyles.SMedium, styles.detailItemValueRight]}
            >{`${formattedMortgageAmount} ${t("NFTs")}`}</Text>
          </View>
        </View>
      </View>
      <View style={styles.detailsRow}>
        <View style={styles.detailItemContainer}>
          <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Principal")}</Text>
          <View style={styles.amountContainer}>
            <Text style={textStyles.SSemiBold}>{formatedPrincipal}</Text>
            <Image source={{ uri: tokenImageUrl }} style={viewStyles.size8Icon} />
          </View>
        </View>
        <View style={styles.detailItemContainer}>
          <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("APR")}</Text>

          <Text style={styles.apr}>{`${apr}%`}</Text>
        </View>
      </View>

      <View style={[styles.detailsRow, styles.marginTop12]}>
        <View style={styles.detailItemContainer}>
          <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Repayment")}</Text>
          <View style={styles.amountContainer}>
            <Text style={textStyles.SSemiBold}>{formatedRepayment}</Text>
            <Image source={{ uri: tokenImageUrl }} style={viewStyles.size8Icon} />
          </View>
        </View>
        <View style={styles.detailItemContainer}>
          <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Duration")}</Text>

          <Text style={textStyles.SMedium}>{convertSecondsToTime(durationInSeconds)}</Text>
        </View>
      </View>
      {dueInSeconds > 0 && (
        <View style={styles.dueDate}>
          <Text style={styles.duration}>{t("Due date")}</Text>
          <Text style={textStyles.SMedium}>{dueDate}</Text>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  marginTop12: {
    marginTop: 12,
  },
  cardContainer: {
    flexDirection: "column",
    alignItems: "center",
    paddingVertical: 16,
    gap: 12,
    width: "100%",
    borderBottomWidth: 1,
    borderBottomColor: Colors.Neutral950,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "space-between",
    alignItems: "center",
  },
  detailsSection: {
    flexDirection: "column",
    alignItems: "flex-start",
    marginVertical: 12,
    width: "100%",
  },
  detailsRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    width: "100%",
  },
  detailItemContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    alignContent: "center",
    paddingHorizontal: 8,
    gap: 4,
    height: 28,
    borderWidth: 1,
    borderColor: Colors.Neutral950,
    borderRadius: 4,
  },
  detailItemLabel: {
    color: Colors.Neutral500,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailItemValueRight: {
    textAlign: "right",
    flexShrink: 1,
  },
  amountContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  actionButton: {
    alignSelf: "flex-end",
  },
  duration: {
    ...textStyles.SMedium,
    color: Colors.Neutral500,
    marginHorizontal: 4,
  },
  label: {
    ...textStyles.SMedium,
    color: Colors.Neutral500,
  },
  address: {
    ...textStyles.SMedium,
    color: Colors.Secondary300,
    marginStart: 4,
  },
  rowBetween: {
    flex: 1,
    marginStart: 8,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  avatar: {
    marginStart: 4,
  },
  state: {
    alignContent: "center",
  },
  apr: {
    ...textStyles.SMedium,
    color: Colors.Primary300,
  },
  dueDate: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 6,
    marginTop: 12,
  },
})

export default LoanAttributesView
