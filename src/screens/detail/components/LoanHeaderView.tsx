import React from "react"
import { View, StyleSheet, Image, Text } from "react-native"
import Colors from "@/config/colors"
import { CustomPressable } from "@/components"
import { Divider } from "react-native-paper"
import { useAppNavigaton } from "src/navigation/Routers"
import icBack from "assets/images/ic_chevron_left.png"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "@/config/styles"
import { LoanStateView } from "src/screens/shared/components"

interface LoanDetailHeaderProps {
  state?: string
}

const LoanDetailHeader: React.FC<LoanDetailHeaderProps> = ({ state }) => {
  const { t } = useTranslation()
  const navigation = useAppNavigaton()

  return (
    <>
      <View style={styles.header}>
        <CustomPressable onPress={() => navigation.goBack()}>
          <Image source={icBack} style={viewStyles.size18Icon} />
        </CustomPressable>
        <Text style={styles.title} numberOfLines={1}>
          {t("Detail")}
        </Text>
        {state && (
          <View style={styles.headerRightContainer}>
            <LoanStateView state={state} />
          </View>
        )}
      </View>
      <Divider style={styles.divider} />
    </>
  )
}

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    backgroundColor: Colors.PalleteBlack,
    paddingVertical: 20,
    paddingHorizontal: 16,
    gap: 10,
  },
  title: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  headerRightContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  divider: {
    height: 1,
    width: "100%",
    backgroundColor: Colors.Neutral900,
  },
})

export default LoanDetailHeader
