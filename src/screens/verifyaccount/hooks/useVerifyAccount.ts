import { z } from "zod"
import { useMutation } from "@tanstack/react-query"
import { getProfile<PERSON>pi, verifyPro<PERSON>le<PERSON><PERSON> } from "src/service/index"
import { User } from "@/service/types"
import { useHandleError } from "@/service/errors/handleError"
import { useTranslation } from "react-i18next"
import { showSuccess } from "@/utils/toast"
import { getImageType, getPhotoFileName } from "@/utils/choosePhotoExt"
import { useNavigation } from "@react-navigation/native"
import { NavigationProp, ParamListBase } from "@react-navigation/native"
import { useForm, UseFormReturn } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import useAuthStore from "@/stores/authStore"

type VerifyAccountFormData = {
  fullName: string
  dateOfBirthInSeconds: string
  nationalId: string
  nationalIDCardFrontImage: {
    uri: string
    fileName: string
    width?: number
    height?: number
    type?: string
  }
  nationalIDCardBackImage: {
    uri: string
    fileName: string
    width?: number
    height?: number
    type?: string
  }
  country: string
}

export const useVerifyAccountFormSchema = (
  profile?: User,
): {
  formSchema: z.ZodType<VerifyAccountFormData>
  form: UseFormReturn<VerifyAccountFormData>
} => {
  const { t } = useTranslation()

  const imagePickerAssetSchema = z.object({
    uri: z.string(),
    fileName: z.string(),
    width: z.number().optional(),
    height: z.number().optional(),
    type: z.string().optional(),
  })

  const schema = z.object({
    fullName: z
      .string()
      .min(2, t("Full name must be at least 2 characters"))
      .max(100, t("Full name must be at most 100 characters")),
    dateOfBirthInSeconds: z
      .string()
      .min(1, t("Please select Date of birth"))
      .refine(
        (date) => {
          const today = new Date()
          const birthDate = new Date(date)
          let age = today.getFullYear() - birthDate.getFullYear()
          const monthDiff = today.getMonth() - birthDate.getMonth()
          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--
          }

          return age >= 18
        },
        {
          message: t("You must be at least 18 years old"),
        },
      ),
    nationalId: z
      .string()
      .min(9, t("Citizen ID must be at least 9 characters"))
      .max(12, t("Citizen ID must be at most 12 characters")),
    nationalIDCardFrontImage: imagePickerAssetSchema.refine(
      (data) => data?.uri !== undefined || data?.fileName !== undefined,
    ),
    nationalIDCardBackImage: imagePickerAssetSchema.refine(
      (data) => data?.uri !== undefined || data?.fileName !== undefined,
    ),
    country: z.string(),
  })

  const form = useForm<VerifyAccountFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      fullName: profile?.alias || "",
      nationalId: profile?.nationality || "",
      dateOfBirthInSeconds: "",
      nationalIDCardFrontImage: undefined,
      nationalIDCardBackImage: undefined,
      country: "VIETNAM",
    },
  })

  return {
    formSchema: schema,
    form,
  }
}

export type VerifyAccountFormSchema = ReturnType<typeof useVerifyAccountFormSchema>["formSchema"]
export type VerifyAccountPayload = VerifyAccountFormData

export const useVerifyAccount = (address: string) => {
  const { t } = useTranslation()
  const { handleError } = useHandleError()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const { user: profile, setUser } = useAuthStore()

  const mutateVerifyProfile = useMutation({
    mutationFn: (body: FormData) => verifyProfileApi(body),
    onSuccess: async (data) => {
      if (data) {
        showSuccess(t("Verify account success"))
        mutationGetProfile.mutate()
      }
    },
    onError: (err) => {
      handleError(err, t("Verify account fail"))
    },
  })

  const mutationGetProfile = useMutation({
    mutationFn: () => {
      return getProfileApi(address)
    },
    onSuccess: (userProfile: User) => {
      setUser(userProfile)
    },
    onSettled() {
      onGoBack()
    },
  })

  const onGoBack = () => {
    navigation.goBack()
  }

  const onSubmit = (data: VerifyAccountPayload) => {
    const timestampInSeconds = Math.floor(new Date(data.dateOfBirthInSeconds).getTime() / 1000)

    const body = new FormData()
    body.append("fullName", data.fullName)
    body.append("nationalId", data.nationalId)
    body.append("zone", data.country)

    const frontImageFileName = getPhotoFileName(
      data.nationalIDCardFrontImage.fileName || null,
      data.nationalIDCardFrontImage.uri,
    )
    const frontImageType = getImageType(frontImageFileName)

    const backImageFileName = getPhotoFileName(
      data.nationalIDCardBackImage.fileName || null,
      data.nationalIDCardBackImage.uri,
    )
    const backImageType = getImageType(backImageFileName)

    // @ts-expect-error FormData in React Native expects a file object for images
    body.append("nationalIDCardFrontImage", {
      uri: data.nationalIDCardFrontImage?.uri,
      name: frontImageFileName,
      type: frontImageType,
    })
    // @ts-expect-error FormData in React Native expects a file object for images
    body.append("nationalIDCardBackImage", {
      uri: data.nationalIDCardBackImage?.uri,
      name: backImageFileName,
      type: backImageType,
    })
    body.append("dateOfBirthInSeconds", timestampInSeconds.toString())

    mutateVerifyProfile.mutate(body)
  }

  const countriesData = [{ label: t("VIETNAM"), value: "VIETNAM" }]

  return {
    countriesData,
    onSubmit,
    isLoading: mutateVerifyProfile.isPending || mutationGetProfile.isPending,
    profile,
  }
}

export type Country = {
  label: string
  value: string
}
