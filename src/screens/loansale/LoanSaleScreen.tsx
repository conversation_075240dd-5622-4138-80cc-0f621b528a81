import React from "react"
import { LoanSaleContext } from "./context/LoanSaleContext"
import { useLoanSale } from "./hooks/useLoanSale"
import LoanSaleView from "./LoanSaleView"

const LoanSaleScreen: React.FC = () => {
  const { contextValue } = useLoanSale()
  return (
    <LoanSaleContext.Provider value={contextValue}>
      <LoanSaleView />
    </LoanSaleContext.Provider>
  )
}

export default LoanSaleScreen
