import React, { useCallback } from "react"
import { StyleSheet, FlatList, View, Text, RefreshControl } from "react-native"
import { useLoanSaleContext } from "./context/LoanSaleContext"
import { EmptyView, Background } from "@/components"
import { useTranslation } from "react-i18next"
import { Offer } from "@/service/types"
import { GridLoanSaleMarketplaceItem } from "@/screens/shared/components"
import { textStyles } from "@/config/styles"

const LoanSaleView: React.FC = () => {
  const { offers = [], totalOffers, isLoading, error, handleRefresh } = useLoanSaleContext()
  const { t } = useTranslation()

  const renderItem = useCallback(
    ({ item }: { item: Offer }) => <GridLoanSaleMarketplaceItem item={item} />,
    [],
  )

  const title = t("{{count}} Loan Sales", { count: totalOffers })

  const renderContent = () => {
    if (isLoading) {
      return null
    }

    if (error) {
      return <EmptyView subtitle={t("Failed to load loan sale")} />
    }

    if (!offers.length) {
      return <EmptyView subtitle={t("No loan sale found")} />
    }

    return (
      <View style={styles.container}>
        <Text style={styles.title}>{title}</Text>
        <FlatList
          data={offers}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          numColumns={2}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.flatListContent}
          columnWrapperStyle={styles.columnWrapper}
          refreshControl={<RefreshControl refreshing={isLoading} onRefresh={handleRefresh} />}
        />
      </View>
    )
  }

  return <Background>{renderContent()}</Background>
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    ...textStyles.size2XLMedium,
    margin: 16,
  },
  flatListContent: {
    paddingHorizontal: 16,
  },
  columnWrapper: {
    justifyContent: "space-between",
  },
})

export default LoanSaleView
