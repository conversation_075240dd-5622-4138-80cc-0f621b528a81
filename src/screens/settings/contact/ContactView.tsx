import React, { useCallback } from "react"
import { Image, Linking, StyleSheet, View, ImageSourcePropType, FlatList } from "react-native"
import { Background, TopBar } from "src/components"
import { SelectItemView } from "src/screens/shared/components"
import { useTranslation } from "react-i18next"
import twitterIcon from "assets/images/ic_twitter.png"
import { viewStyles } from "src/config/styles"

interface ContactItemProps {
  title: string
  icon: ImageSourcePropType
  onPress: () => void
}

const ContactView: React.FC = () => {
  const { t } = useTranslation()

  const onSelectX = () => {
    Linking.openURL("https://x.com/brikyfinance?s=21")
  }

  const contactItems: ContactItemProps[] = [
    {
      title: t("X"),
      icon: twitterIcon,
      onPress: onSelectX,
    },
  ]

  const renderItem = useCallback(
    ({ item }: { item: ContactItemProps }) => (
      <SelectItemView
        title={item.title}
        icon={<Image source={item.icon} style={viewStyles.size18Icon} />}
        onPress={item.onPress}
        showNextIcon={false}
      />
    ),
    [],
  )

  return (
    <Background>
      <TopBar enableBack={true} title={t("Contact")} />
      <View style={styles.container}>
        <FlatList data={contactItems} renderItem={renderItem} keyExtractor={(item) => item.title} />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
})

export { ContactView }
