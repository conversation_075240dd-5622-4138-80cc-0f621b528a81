import { useTranslation } from "react-i18next"
import { Office } from "../types"

const useOfficesData = () => {
  const { t } = useTranslation()

  const officesData: Office[] = [
    {
      nationalName: t("Viet Nam Office"),
      companyName: t("Briky Land Real Estate Trading Floor Joint Stock Company"),
      branches: [
        {
          headOfficeName: t("Tax registration office"),
          address: t(
            "No. 1A, Lane 57 Nguyen Khanh Toan, Dich Vong Ward, Cau Giay District, Hanoi City.",
          ),
        },
        {
          headOfficeName: t("Hanoi Office 1 - real estate floor headquarters"),
          address: t(
            "Building N01-T2, Diplomatic Corps Urban Area, Hoang Minh Thao Street, Xuan Tao Ward, Bac Tu Liem District, Hanoi.",
          ),
        },
        {
          headOfficeName: t("Hanoi Office 2"),
          address: t(
            "5th Floor, SME Royal Building, To Hieu Street, Quang Trung Ward, Ha Dong District, Hanoi.",
          ),
        },
        {
          headOfficeName: t("Ho Chi Minh Office"),
          address: t("178-180 Le Hong Phong Street, Ward 3, District 5, Ho Chi Minh City."),
        },
      ],
    },
    {
      nationalName: t("Singapore Office"),
      companyName: t("Brikyland Holding Pte.Ltd"),
      branches: [
        {
          headOfficeName: "",
          address: t("114 Lavender Street, #11-83 CT HUB 2, Singapore."),
        },
      ],
    },
    {
      nationalName: t("Australia Office"),
      companyName: t("Brikyland Australia Pty.Ltd"),
      branches: [
        {
          headOfficeName: "",
          address: t("Suit 886, 100 George Street, Parramatta NSW 2150 Australia."),
        },
      ],
    },
    {
      nationalName: t("Dubai Office"),
      companyName: t("Briky Land Virtual Assets Management Investment Services L.L.C"),
    },
  ]
  return officesData
}

export { useOfficesData }
