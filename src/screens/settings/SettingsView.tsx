import React from "react"
import { Image, Linking, StyleSheet, Text, View } from "react-native"
import { Background } from "src/components"
import { SelectItemView } from "src/screens/shared/components"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { getLanguageLabel } from "./types"
import { BASE_WEB_URL } from "src/config/env"
import icLanguages from "assets/images/ic_languages.png"
import icBookText from "assets/images/ic_book_text.png"
import icContact from "assets/images/ic_contact.png"
import icHome from "assets/images/ic_home.png"
import icUserX from "assets/images/ic_user_x.png"
import { Divider } from "react-native-paper"
import * as Routes from "src/navigation/Routers"
import { useAppNavigaton } from "src/navigation/Routers"

const SettingsView: React.FC = () => {
  const { t, i18n } = useTranslation()
  const navigation = useAppNavigaton()

  const onGoHome = () => {
    navigation.navigate(Routes.HOME)
  }

  const onSelectLanguage = () => {
    // navigation.navigate(Routes.SELECT_LANGUAGE, {
    //   selectedLanguage: i18n.language,
    // })
  }
  const onSelectReferences = () => {
    navigation.navigate(Routes.REFERENCES)
  }

  const onSelectContact = () => {
    navigation.navigate(Routes.CONTACT)
  }

  const onSelectDeleteAccount = async () => {
    onGoHome()
    Linking.openURL(`${BASE_WEB_URL}/delete-account`)
  }

  const onViewOffices = () => {
    navigation.navigate(Routes.OFFICES)
  }

  return (
    <Background>
      <View style={styles.container}>
        <Text style={styles.label}>{t("Settings")}</Text>
        <SelectItemView
          note={getLanguageLabel(i18n.language)}
          title={t("Language")}
          // TODO: tạm thời chỉ có tiếng Anh nên sẽ không cho chọn ngôn ngữ
          clickable={false}
          icon={<Image source={icLanguages} style={viewStyles.size18Icon} />}
          showNextIcon={false}
          onPress={onSelectLanguage}
        />
        <Divider style={styles.divider} />
        <Text style={styles.label}>{t("Informations")}</Text>
        <SelectItemView
          title={t("References")}
          icon={<Image source={icBookText} style={viewStyles.size18Icon} />}
          onPress={onSelectReferences}
        />
        <SelectItemView
          title={t("Contact")}
          icon={<Image source={icContact} style={viewStyles.size18Icon} />}
          onPress={onSelectContact}
        />
        <SelectItemView
          title={t("Offices")}
          icon={<Image source={icHome} style={viewStyles.size18Icon} />}
          onPress={onViewOffices}
        />
        <Divider style={styles.divider} />
        <Text style={styles.label}>{t("Other")}</Text>
        <SelectItemView
          title={t("Delete account")}
          icon={<Image source={icUserX} style={viewStyles.size18Icon} />}
          onPress={onSelectDeleteAccount}
        />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    ...textStyles.LSemiBold,
    color: Colors.Neutral700,
    marginVertical: 12,
  },
  divider: {
    height: 1,
    width: "100%",
    backgroundColor: Colors.Neutral900,
    marginVertical: 16,
  },
})

export { SettingsView }
