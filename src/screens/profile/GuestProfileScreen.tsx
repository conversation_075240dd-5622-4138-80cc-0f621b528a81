import * as React from "react"
import { ProfileContext } from "./context/ProfileContext"
import ProfileView from "./ProfileView"
import { useGuestProfile } from "./hooks/useGuestProfile"

const GuestProfileScreen = () => {
  const { contextValue } = useGuestProfile()
  return (
    <ProfileContext.Provider value={contextValue}>
      <ProfileView />
    </ProfileContext.Provider>
  )
}

export default GuestProfileScreen
