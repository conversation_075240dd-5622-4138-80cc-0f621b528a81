import React, { useMemo } from "react"
import { StyleSheet, SectionList, View } from "react-native"
import Colors from "@/config/colors"
import { textStyles } from "@/config/styles"
import ProfileHeader from "./components/ProfileHeader"
import ConnectWalletView from "./components/ConnectWalletView"
import useAuthStore from "@/stores/authStore"
import { Background } from "@/components/Background"
import TabSelector from "../shared/components/TabSelector"
import { useTranslation } from "react-i18next"
import { useProfileContext } from "./context/ProfileContext"
import { LoanSaleTab, MyLoanTab, DisbursedLoanTab } from "./components"

// Define item types for our SectionList
interface SectionItem {
  type: string
  id?: string
  data?: any
}

// Define our section types
interface ProfileSection {
  type: string
  data: SectionItem[]
}

const ProfileView: React.FC = () => {
  const { t } = useTranslation()
  const { isAuthenticated } = useAuthStore()
  const { selectedTabIndex, setSelectedTabIndex } = useProfileContext()

  // Generate sections for our SectionList
  const sections = useMemo(() => {
    return [
      {
        type: "header",
        data: [{ type: "header" }],
      },
      {
        type: "tab",
        data: [{ type: "tab" }],
      },
    ] as ProfileSection[]
  }, [])

  // UI khi chưa đăng nhập
  if (!isAuthenticated) {
    return (
      <Background>
        <ConnectWalletView />
      </Background>
    )
  }

  const renderItem = ({ section }: { section: ProfileSection }) => {
    if (section.type === "header") {
      return <ProfileHeader />
    }
    if (section.type === "tab") {
      return (
        <View style={styles.tabContainer}>
          {selectedTabIndex === 0 ? <MyLoanTab /> : null}
          {selectedTabIndex === 1 ? <DisbursedLoanTab /> : null}
          {selectedTabIndex === 2 ? <LoanSaleTab /> : null}
        </View>
      )
    }
    return null
  }

  // Render different content based on section type
  const renderSectionHeader = ({ section }: { section: ProfileSection }) => {
    const tabTitles = [t("My Loan"), t("Disbursed Loans"), t("Loan Sale")]
    if (section.type === "tab") {
      return (
        <View style={styles.tabSelectorContainer}>
          <TabSelector
            tabTitles={tabTitles}
            selectedIndex={selectedTabIndex}
            setTabIndex={setSelectedTabIndex}
          />
        </View>
      )
    }
    return null
  }

  // UI khi đã đăng nhập with SectionList
  return (
    <SectionList
      style={styles.container}
      sections={sections}
      renderItem={renderItem}
      renderSectionHeader={renderSectionHeader}
      stickySectionHeadersEnabled={true}
      keyExtractor={(item, index) =>
        item.type === "header" ? `header-${index}` : `tab-${item.id || index}`
      }
    />
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.PalleteBlack,
  },
  content: {
    flex: 1,
  },
  headerContainer: {
    backgroundColor: Colors.PalleteBlack,
  },
  tabSelectorContainer: {
    paddingHorizontal: 16,
    backgroundColor: Colors.PalleteBlack,
  },
  tabContainer: {
    paddingHorizontal: 16,
    paddingBottom: 30,
  },
  tabButton: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    position: "relative",
  },
  tabText: {
    ...textStyles.MSemiBold,
    color: Colors.Neutral500,
    textAlign: "center",
  },
  selectedTabText: {
    color: Colors.Primary500,
  },
  activeIndicator: {
    position: "absolute",
    bottom: 0,
    left: 4,
    right: 4,
    height: 1,
    backgroundColor: Colors.Primary500,
  },
  tabContent: {
    flex: 1,
    backgroundColor: Colors.PalleteBlack,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
  },
  // Styles cho UI chưa đăng nhập
  notLoggedInContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  notLoggedInTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.PalleteWhite,
    marginBottom: 12,
    textAlign: "center",
  },
  notLoggedInDescription: {
    fontSize: 16,
    color: Colors.Neutral500,
    marginBottom: 40,
    textAlign: "center",
  },
  loginImage: {
    width: 200,
    height: 200,
    marginBottom: 40,
  },
  loginButton: {
    width: "80%",
    marginTop: 20,
  },
})

export default ProfileView
