import { User, WalletAddress } from "@/service/types"
import React from "react"

interface ProfileContextType {
  address?: WalletAddress
  user?: User
  isGuest: boolean
  selectedTabIndex: number
  setSelectedTabIndex: React.Dispatch<React.SetStateAction<number>>
}

export const ProfileContext = React.createContext<ProfileContextType>({
  address: undefined,
  user: undefined,
  isGuest: false,
  selectedTabIndex: 0,
  setSelectedTabIndex: () => {},
})

export const useProfileContext = () => {
  return React.useContext(ProfileContext)
}
