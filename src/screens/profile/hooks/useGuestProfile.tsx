import { GUEST_PROFILE, RootStackParamList } from "@/navigation/Routers"
import { getProfileApi } from "@/service"
import { WalletAddress } from "@/service/types"
import useAuthStore from "@/stores/authStore"
import useLoadingStore from "@/stores/loadingStore"
import { RouteProp, useRoute } from "@react-navigation/native"
import { useQuery } from "@tanstack/react-query"
import { useEffect } from "react"

type GuestProfileRouteProps = RouteProp<RootStackParamList, typeof GUEST_PROFILE> & {
  params: {
    address?: string
  }
}

export const useGuestProfile = () => {
  const { address } = useAuthStore()
  const route = useRoute<GuestProfileRouteProps>()
  const { setLoading } = useLoadingStore()
  const walletAddress = route.params?.address
  const { data: user = undefined, isLoading } = useQuery({
    queryKey: ["profile"],
    queryFn: () => getProfile<PERSON><PERSON>(walletAddress),
  })

  useEffect(() => {
    setLoading(isLoading)
    return () => {
      setLoading(false)
    }
  }, [isLoading, setLoading])

  const contextValue = {
    address: walletAddress as WalletAddress,
    user,
    isGuest: address?.toLowerCase() === walletAddress?.toLowerCase(),
    selectedTabIndex: 0,
    setSelectedTabIndex: () => {},
  }
  return { contextValue }
}
