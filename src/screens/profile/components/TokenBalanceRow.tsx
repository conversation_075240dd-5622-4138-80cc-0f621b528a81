import React, { useMemo } from "react"
import {
  View,
  Text,
  Image,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ImageStyle,
  StyleProp,
  ImageRequireSource,
} from "react-native"
import { useAccount } from "wagmi"

// Config & Utils
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { parseCurrency } from "src/utils/format"
import {
  CONTRACT_ADDRESS_LIQUIDATION_CURRENCY,
  CONTRACT_ADDRESS_PRIMARY_TOKEN,
  CONTRACT_ADDRESS_STAKE_TOKEN_1,
} from "@/config/env"

// API & Hooks
import { useErc20Balance, useErc20Formatter } from "@/contracts"

// Assets
import iconBrik from "assets/images/ic_brik.png"
import iconBrikStake from "assets/images/ic_brikstake.png"
import iconUSDT from "assets/images/ic_usdt.png"

// Types
interface TokenCardProps {
  iconUri: ImageRequireSource
  tokenAmount: string
  tokenName?: string
  style?: StyleProp<ViewStyle>
}

interface TokenData {
  icon: ImageRequireSource
  address: string
  balance: string
  name?: string
}

/**
 * Formats token balance with proper decimal places
 */
export const formatTokenBalance = (balance: any, formatter: any): string => {
  if (!balance || !formatter) return "0.00"
  return parseCurrency(formatter.formatFixed(balance))
}

/**
 * TokenCard component displays a single token's balance and value
 */
const TokenCard: React.FC<TokenCardProps> = ({ iconUri, tokenAmount, tokenName, style }) => {
  return (
    <View style={[styles.tokenCardContainer, style]}>
      <View style={styles.innerContentContainer}>
        <View style={styles.topRow}>
          <Image source={iconUri} style={[styles.iconImage, !iconUri && styles.imagePlaceholder]} />
          <View>
            <Text style={styles.valueText}>{tokenAmount}</Text>
            <Text style={styles.valueText}>{tokenName}</Text>
          </View>
        </View>
      </View>
    </View>
  )
}

/**
 * TokenBalanceRow component displays a row of token balances
 */
const TokenBalanceRow: React.FC = () => {
  const { address: userAddress } = useAccount()

  // Retrieve token balances and their formatters
  const currencyTokenBalance = useErc20Balance(userAddress, CONTRACT_ADDRESS_LIQUIDATION_CURRENCY)
  const currencyFormatter = useErc20Formatter(CONTRACT_ADDRESS_LIQUIDATION_CURRENCY)

  const primaryTokenBalance = useErc20Balance(userAddress, CONTRACT_ADDRESS_PRIMARY_TOKEN)
  const primaryTokenFormatter = useErc20Formatter(CONTRACT_ADDRESS_PRIMARY_TOKEN)

  const stakeTokenBalance = useErc20Balance(userAddress, CONTRACT_ADDRESS_STAKE_TOKEN_1)
  const stakeTokenFormatter = useErc20Formatter(CONTRACT_ADDRESS_STAKE_TOKEN_1)

  // Prepare token data with memoization
  const tokenData = useMemo<TokenData[]>(() => {
    return [
      {
        icon: iconUSDT,
        address: CONTRACT_ADDRESS_LIQUIDATION_CURRENCY,
        balance: formatTokenBalance(currencyTokenBalance, currencyFormatter),
        name: "USDT",
      },
      {
        icon: iconBrik,
        address: CONTRACT_ADDRESS_PRIMARY_TOKEN,
        balance: formatTokenBalance(primaryTokenBalance, primaryTokenFormatter),
        name: "BRIK",
      },
      {
        icon: iconBrikStake,
        address: CONTRACT_ADDRESS_STAKE_TOKEN_1,
        balance: formatTokenBalance(stakeTokenBalance, stakeTokenFormatter),
        name: "BRIKSTAKE",
      },
    ]
  }, [
    currencyTokenBalance,
    currencyFormatter,
    primaryTokenBalance,
    primaryTokenFormatter,
    stakeTokenBalance,
    stakeTokenFormatter,
  ])

  return (
    <View style={styles.tokenRowContainer}>
      {tokenData.map((token, index) => (
        <TokenCard
          key={token.address ?? index}
          iconUri={token.icon}
          tokenAmount={token.balance}
          tokenName={token.name}
        />
      ))}
    </View>
  )
}

// Styles
const styles = StyleSheet.create({
  tokenRowContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 8,
    width: "100%",
  } as ViewStyle,

  tokenCardContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 8,
    backgroundColor: Colors.Neutral950,
    borderRadius: 4,
    flex: 1,
  } as ViewStyle,

  innerContentContainer: {
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    padding: 0,
    gap: 8,
    alignSelf: "stretch",
    flex: 1,
  } as ViewStyle,

  topRow: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    width: "100%",
    height: 24,
    alignSelf: "stretch",
    gap: 6,
  } as ViewStyle,

  iconImage: {
    width: 24,
    height: 24,
    resizeMode: "contain",
  } as ImageStyle,

  valueText: {
    ...textStyles.SMedium,
    letterSpacing: -0.4,
    lineHeight: 12,
    color: Colors.PalleteWhite,
  } as TextStyle,

  imagePlaceholder: {
    backgroundColor: Colors.Neutral800,
    borderRadius: 4,
  },
})

export default TokenBalanceRow
