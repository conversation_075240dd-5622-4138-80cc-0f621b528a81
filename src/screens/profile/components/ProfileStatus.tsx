import React from "react"
import { StyleSheet, Text, Image, ImageRequireSource, Pressable } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import iconCircleCheck from "assets/images/ic_circle_check.png"
import iconLoader from "assets/images/ic_loader.png"
import { useTranslation } from "react-i18next"
import { useProfileContext } from "../context/ProfileContext"
import { useAppNavigaton, VERIFY_ACCOUNT } from "@/navigation/Routers"

interface ProfileStatusProps {
  status: string
}

type StatusKey = "VERIFIED" | "UNVERIFIED" | "VERIFIING"

type StatusConfigType = {
  icon: ImageRequireSource
  contentColor: string
  backgroundColor: string
  borderColor: string
  label: string
}

const useStatusConfigs = (status: StatusKey) => {
  const { t } = useTranslation()
  const configs = {
    VERIFIED: {
      icon: iconCircleCheck,
      contentColor: Colors.Success300,
      backgroundColor: Colors.Success900,
      borderColor: Colors.Success900,
      label: t("Account Verified"),
    },
    UNVERIFIED: {
      icon: iconCircleCheck,
      contentColor: Colors.PalleteWhite,
      backgroundColor: "transparent",
      borderColor: Colors.Neutral900,
      label: t("Verify my account"),
    },
    VERIFIING: {
      icon: iconLoader,
      contentColor: Colors.Primary300,
      backgroundColor: Colors.Primary900,
      borderColor: Colors.Primary800,
      label: t("Account Verifying"),
    },
  }
  return (configs[status] || {
    icon: iconCircleCheck,
    contentColor: Colors.PalleteWhite,
    backgroundColor: "transparent",
    borderColor: Colors.Neutral900,
    label: t("Verify my account"),
  }) as StatusConfigType
}

const ProfileStatus: React.FC<ProfileStatusProps> = ({ status }) => {
  const config = useStatusConfigs(status as StatusKey)
  const { isGuest } = useProfileContext()
  const navigation = useAppNavigaton()
  return (
    <Pressable
      disabled={status === "UNVERIFIED" && !isGuest}
      onPress={() => {
        navigation.navigate(VERIFY_ACCOUNT)
      }}
      style={[
        styles.statusContainer,
        {
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor,
        },
      ]}
    >
      <Image source={config.icon} style={styles.statusIcon} tintColor={config.contentColor} />
      <Text style={[textStyles.SMedium, { color: config.contentColor }]}>{config.label}</Text>
    </Pressable>
  )
}

const styles = StyleSheet.create({
  statusContainer: {
    borderRadius: 4,
    paddingHorizontal: 8,
    height: 24,
    marginTop: 16,
    backgroundColor: Colors.Success900,
    alignItems: "center",
    alignSelf: "flex-start",
    borderWidth: 1,
    flexDirection: "row",
    gap: 4,
  },
  statusIcon: {
    width: 10,
    height: 10,
  },
})

export default ProfileStatus
