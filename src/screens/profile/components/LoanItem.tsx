import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "@/config/colors"
import { textStyles } from "@/config/styles"
import { Estate, EstateZone, MortgageLoan } from "@/service/types"
import iconMapPin from "assets/images/ic_map_pin.png"
import iconBoxLock from "assets/images/ic_box_lock.png"
import iconBadgePercent from "assets/images/ic_badge_percent.png"
import { useCurrencies } from "@/screens/shared/hooks/useCurrencies"
import { formatCurrencyByDecimals } from "@/utils"
import { convertDateFromTimeStamp, DateTimeFormat } from "@/utils/timeExt"
import LoanState from "@/screens/shared/components/LoanStateView"

interface LoanItemProps {
  item: MortgageLoan
  estate?: Estate
  actionButton: React.ReactNode
}

const LoanItem: React.FC<LoanItemProps> = ({ item, estate, actionButton }) => {
  const { t } = useTranslation()
  const {
    metadata: {
      metadata: { name },
      imageUrl,
    },
  } = estate
  const formatedTime = convertDateFromTimeStamp(item.dueInSeconds * 1000, DateTimeFormat.SHORT)
  const formatedPrincipal = formatCurrencyByDecimals(item.principal, estate.decimals)
  const formatedRepayment = formatCurrencyByDecimals(item.repayment, estate.decimals)
  const formatedTotalAmount = formatCurrencyByDecimals(item.mortgageAmount, estate.decimals)
  const currency = item.currency
  const { tokenImageUrl } = useCurrencies(currency)

  return (
    <View style={styles.cardContainer}>
      <View style={styles.topSection}>
        <Image source={{ uri: imageUrl }} style={styles.propertyImage} />
        <View style={styles.titleAndTagsContainer}>
          <Text style={[textStyles.MMedium, styles.titleText]} numberOfLines={2}>
            {t(name)}
          </Text>
          <View style={styles.tagsRow}>
            <View style={styles.tagItem}>
              <Image source={iconMapPin} style={styles.tagIcon} />
              <Text style={[textStyles.SMedium, styles.tagText]}>{t(EstateZone.VIETNAM)}</Text>
            </View>
            <View style={styles.separatorDot} />
            <View style={styles.tagItem}>
              <Image source={iconBoxLock} style={styles.tagIcon} />
              <Text style={[textStyles.SMedium, styles.tagText]}>{formatedTotalAmount}</Text>
            </View>
            <View style={styles.separatorDot} />
            <View style={styles.tagItem}>
              <Image source={iconBadgePercent} style={styles.tagIcon} />
              <Text style={[textStyles.SMedium, styles.tagText]}>{item.apr}</Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.detailsSection}>
        <View style={styles.detailsRow}>
          <View style={styles.detailItemContainer}>
            <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Status")}</Text>
            <LoanState state={item.state} style={styles.statusBadge} />
          </View>
          <View style={styles.detailItemContainer}>
            <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Due date")}</Text>
            <Text style={[textStyles.SRegular, styles.detailItemValueRight]}>{formatedTime}</Text>
          </View>
        </View>

        <View style={styles.detailsRow}>
          <View style={styles.detailItemContainer}>
            <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Principal")}</Text>
            <View style={styles.amountContainer}>
              <Text style={[textStyles.SSemiBold, styles.amountText]}>{formatedPrincipal}</Text>
              <Image source={{ uri: tokenImageUrl }} style={styles.currencyIcon} />
            </View>
          </View>
          <View style={styles.detailItemContainer}>
            <Text style={[textStyles.SMedium, styles.detailItemLabel]}>{t("Repayment")}</Text>
            <View style={styles.amountContainer}>
              <Text style={[textStyles.SSemiBold, styles.amountText]}>{formatedRepayment}</Text>
              <Image source={{ uri: tokenImageUrl }} style={styles.currencyIcon} />
            </View>
          </View>
        </View>
      </View>
      <View style={styles.actionButton}>{actionButton}</View>
    </View>
  )
}

const styles = StyleSheet.create({
  cardContainer: {
    flexDirection: "column",
    alignItems: "center",
    paddingVertical: 16,
    gap: 12,
    width: "100%", // changed from fixed width 343 to full width of parent
    borderBottomWidth: 1,
    borderBottomColor: Colors.Neutral950,
  },
  topSection: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    width: "100%",
  },
  propertyImage: {
    width: 80,
    height: 42,
    borderRadius: 4,
  },
  titleAndTagsContainer: {
    flex: 1,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "flex-start",
    gap: 6,
  },
  titleText: {
    color: Colors.PalleteWhite,
  },
  tagsRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignItems: "center",
    gap: 4,
    opacity: 0.7,
  },
  tagItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
  },
  tagIcon: {
    width: 10,
    height: 10,
  },
  tagText: {
    color: Colors.Neutral300,
  },
  separatorDot: {
    width: 2,
    height: 2,
    backgroundColor: Colors.Neutral300,
    opacity: 0.5,
    borderRadius: 1,
    marginHorizontal: 2,
  },
  detailsSection: {
    flexDirection: "column",
    alignItems: "flex-start",
    gap: 6,
    width: "100%",
  },
  detailsRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    width: "100%",
  },
  detailItemContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 8,
    gap: 4,
    height: 28,
    borderWidth: 1,
    borderColor: Colors.Neutral950,
    borderRadius: 4,
  },
  detailItemLabel: {
    color: Colors.Neutral300,
  },
  statusBadge: {
    alignSelf: "center",
  },
  statusBadgeDot: {
    width: 8,
    height: 8,
    backgroundColor: Colors.Success500,
    borderRadius: 4,
  },
  statusBadgeText: {
    color: Colors.PalleteWhite,
  },
  detailItemValueRight: {
    color: Colors.PalleteWhite,
    textAlign: "right",
    flexShrink: 1,
  },
  amountContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  amountText: {
    color: Colors.PalleteWhite,
  },
  currencyIcon: {
    width: 8,
    height: 8,
  },
  actionButton: {
    alignSelf: "flex-end",
  },
})

export default LoanItem
