import React from "react"
import { SimplePagingList } from "@/components/simplepaginglist"
import { MortgageLoan } from "@/service/types"
import { getListLoansApi } from "@/service"
import QueryKeys from "@/config/queryKeys"
import LoanItem from "./LoanItem"
import useAuthStore from "@/stores/authStore"
import { LoanActionButton } from "@/screens/shared/components/LoanActionButton"
import { useTranslation } from "react-i18next"
import { useLoanData } from "@/service/hooks/useLoanData"

const renderItem = (item: MortgageLoan) => {
  return (
    <LoanItem item={item} estate={item.estate} actionButton={<LoanActionButton loan={item} />} />
  )
}

const MyLoanTab = () => {
  const { t } = useTranslation()
  const { address } = useAuthStore()
  const { getListLoan } = useLoanData()
  return (
    <SimplePagingList<MortgageLoan>
      getData={(params) => getListLoan(() => getListLoansApi({ ...params, borrower: address }))}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      scrollEnabled={false}
      queryKeys={[QueryKeys.PROFILE.MY_LOAN]}
      emptyMessage={t(
        "You haven’t taken out any loans yet, apply for a loan to see it listed here",
      )}
      emptyTitle={t("No Loans Found")}
    />
  )
}

export default MyLoanTab
