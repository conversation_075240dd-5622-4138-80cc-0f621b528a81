import React from "react"
import { StyleSheet, View, Text, Pressable, ImageBackground } from "react-native"
import { useTranslation } from "react-i18next"
import { AvatarView } from "@/components/AvatarView"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useProfileContext } from "../context/ProfileContext"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import UserInfoDetails from "./UserInfoDetails"
import imgProfileBackground from "assets/images/img_profile_background.png"
import TokenBalanceRow from "./TokenBalanceRow"
import ProfileStatus from "./ProfileStatus"
import { PrimaryButton } from "@/components/Button"
import useWalletConnect from "@/hooks/useWalletConnect"
import * as Routes from "src/navigation/Routers"
import { useAppNavigaton } from "src/navigation/Routers"

interface ProfileBackgroundProps {
  onEdit?: () => void
  onShare?: () => void
}

const ProfileBackground: React.FC<ProfileBackgroundProps> = ({ onEdit, onShare }) => {
  const { t } = useTranslation()
  const { isGuest } = useProfileContext()

  return (
    <ImageBackground
      source={imgProfileBackground}
      style={styles.backgroundImage}
      resizeMode="cover"
    >
      {!isGuest && (
        <Pressable style={styles.editButton} onPress={onEdit}>
          <MaterialCommunityIcons
            name="pencil"
            size={12}
            color={Colors.PalleteWhite}
            style={styles.buttonIcon}
          />
          <Text style={styles.editButtonText}>{t("Edit Profile")}</Text>
        </Pressable>
      )}

      <View style={styles.shareButtonContainer}>
        <Pressable style={styles.shareButton} onPress={onShare}>
          <MaterialCommunityIcons
            name="share-variant"
            size={12}
            color={Colors.PalleteWhite}
            style={styles.buttonIcon}
          />
          <Text style={styles.shareButtonText}>{t("Share")}</Text>
        </Pressable>
      </View>
    </ImageBackground>
  )
}

interface ProfileHeaderProps {
  onEdit?: () => void
  onShare?: () => void
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({ onShare }) => {
  const { t } = useTranslation()
  const { user: profile, isGuest } = useProfileContext()
  const { disconnect } = useWalletConnect()
  const navigation = useAppNavigaton()

  if (!profile) {
    return <View style={styles.container} />
  }

  const handleEditProfile = () => {
    navigation.navigate(Routes.EDIT_PROFILE)
  }

  return (
    <View style={styles.container}>
      <ProfileBackground onEdit={handleEditProfile} onShare={onShare} />
      <AvatarView size={64} avatarUrl={profile.avatarUrl} style={styles.avatar} />
      <ProfileStatus status={profile.status} />
      <Text style={styles.nameText}>{profile.alias || "-"}</Text>
      <UserInfoDetails />
      {!isGuest && (
        <PrimaryButton
          title={t("Disconnect")}
          textStyle={[textStyles.LMedium]}
          onPress={() => {
            disconnect()
          }}
          width={"100%"}
          height={32}
          style={styles.disconnectButton}
          contentColor={Colors.PalleteWhite}
          color={Colors.Danger500}
        />
      )}
      <TokenBalanceRow />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.PalleteBlack,
    borderRadius: 8,
    padding: 16,
  },
  backgroundImage: {
    height: 100,
    width: "100%",
    borderRadius: 6,
    backgroundColor: Colors.Neutral950,
    justifyContent: "space-between",
  },
  editButton: {
    flexDirection: "row",
    alignSelf: "flex-end",
    alignItems: "center",
    backgroundColor: Colors.Neutral900,
    height: 24,
    paddingHorizontal: 8,
    borderRadius: 4,
    margin: 4,
  },
  buttonIcon: {
    marginRight: 8,
  },
  editButtonText: {
    ...textStyles.SMedium,
    color: Colors.PalleteWhite,
  },
  shareButtonContainer: {
    flexDirection: "row",
    alignSelf: "flex-end",
    margin: 4,
  },
  shareButton: {
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    flexDirection: "row",
    paddingHorizontal: 8,
  },
  shareButtonText: {
    ...textStyles.SMedium,
    color: Colors.PalleteWhite,
  },
  avatar: {
    marginTop: -32,
    marginStart: 12,
  },
  nameText: {
    ...textStyles.size3XLMedium,
    color: Colors.PalleteWhite,
    marginBottom: 6,
  },
  disconnectButton: {
    marginBottom: 16,
  },
})

export default ProfileHeader
