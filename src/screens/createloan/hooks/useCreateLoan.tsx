import { useMemo, useState } from "react"
import { CreateLoanContextType } from "../context/CreateLoanContext"
import { PublicEstate } from "@/service/types"
import { durationUnitMap } from "@/utils/timeExt"
import { calculateApr } from "@/utils/numberExt"
export const useCreateLoan = () => {
  const [selectedNFT, setSelectedNFT] = useState<PublicEstate | undefined>(undefined)
  const [quantity, setQuantity] = useState<number>(0)
  const [principal, setPrincipal] = useState<string>("")
  const [repayment, setRepayment] = useState<string>("")
  const [duration, setDuration] = useState<number>(0)
  const [durationUnit, setDurationUnit] = useState<string>("day")

  const apr = useMemo(() => {
    // Calculate duration in days
    const durationInSeconds = duration * durationUnitMap[durationUnit]
    return calculateApr(Number(principal), Number(repayment), durationInSeconds)
  }, [principal, repayment, duration, durationUnit])

  const reset = () => {
    setSelectedNFT(undefined)
    setQuantity(0)
    setPrincipal("")
    setRepayment("")
    setDuration(0)
    setDurationUnit("day")
  }

  const contextValue: CreateLoanContextType = {
    selectedNFT,
    setSelectedNFT,
    quantity,
    setQuantity,
    principal,
    setPrincipal,
    repayment,
    setRepayment,
    duration,
    setDuration,
    durationUnit,
    setDurationUnit,
    apr,
    reset,
  }

  return { contextValue }
}
