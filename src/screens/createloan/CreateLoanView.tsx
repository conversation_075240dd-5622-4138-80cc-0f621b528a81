import React from "react"
import { StyleSheet, ScrollView, View } from "react-native"
import { Background } from "@/components/Background"
import { TopBar } from "@/components/TopBar"
import { useTranslation } from "react-i18next"
import Colors from "@/config/colors"
import ListNFTsMortgage from "./components/ListNFTsMortage"
import SelectedNFTInfo from "./components/SelectedNFTInfo"
import LoanDetailsForm from "./components/LoanDetailsForm"

const CreateLoanView = () => {
  const { t } = useTranslation()

  return (
    <Background>
      <TopBar title={t("Create Loan")} enableBack />
      <ScrollView style={styles.container}>
        <ListNFTsMortgage />
        <LoanDetailsForm />
        <SelectedNFTInfo />
        <View style={styles.bottomSpacer} />
      </ScrollView>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.PalleteBlack,
    padding: 8,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.Neutral900,
    backgroundColor: Colors.PalleteBlack,
  },
  bottomSpacer: {
    height: 50,
  },
})

export default CreateLoanView
