import React from "react"
import { PublicEstate } from "@/service/types"
export interface CreateLoanContextType {
  selectedNFT?: PublicEstate
  setSelectedNFT: (nft: PublicEstate) => void
  quantity?: number
  setQuantity: (quantity: number) => void
  principal: string
  setPrincipal: (principal: string) => void
  repayment: string
  setRepayment: (repayment: string) => void
  duration: number
  setDuration: (duration: number) => void
  durationUnit: string
  setDurationUnit: (durationUnit: string) => void
  apr: string
  reset: () => void
}

export const CreateLoanContext = React.createContext<CreateLoanContextType>({
  selectedNFT: undefined,
  setSelectedNFT: () => {},
  quantity: 0,
  setQuantity: () => {},
  principal: "",
  setPrincipal: () => {},
  repayment: "",
  setRepayment: () => {},
  duration: 0,
  setDuration: () => {},
  durationUnit: "day",
  setDurationUnit: () => {},
  apr: "0.0",
  reset: () => {},
})

export const useCreateLoanContext = () => {
  return React.useContext(CreateLoanContext)
}
