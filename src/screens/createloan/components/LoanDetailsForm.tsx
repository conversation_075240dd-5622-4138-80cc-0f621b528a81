import React, { useCallback } from "react"
import { View, StyleSheet, Text, Image } from "react-native"
import { useTranslation } from "react-i18next"
import { InputField } from "@/components/InputField"
import { textStyles, viewStyles } from "@/config/styles"
import iconUSDT from "assets/images/ic_usdt.png"
import { LabelView } from "@/components/LabelView"
import Colors from "@/config/colors"
import { useCreateLoanContext } from "../context/CreateLoanContext"
import { Dropdown } from "react-native-element-dropdown"
import { durationUnitMap } from "@/utils/timeExt"

const DropdownItem = React.memo(({ label }: { label: string }) => (
  <Text style={styles.renderDropdownItem}>{label}</Text>
))

const LoanDetailsForm: React.FC = () => {
  const { t } = useTranslation()
  const {
    principal,
    repayment,
    duration,
    apr,
    setPrincipal,
    setRepayment,
    setDuration,
    durationUnit,
    setDurationUnit,
  } = useCreateLoanContext()

  const renderDropdownItem = useCallback(
    (item: { label: string }) => <DropdownItem label={item.label} />,
    [],
  )

  return (
    <View style={styles.formContainer}>
      <View style={styles.rowContainer}>
        <InputField
          label={t("Principal")}
          value={principal.toString()}
          height={32}
          type="number"
          placeholder="0.00"
          onChangeText={(text) => setPrincipal(text.toString())}
          style={styles.inputField}
          trailingIcon={<Image source={iconUSDT} style={viewStyles.size12Icon} />}
        />
        <InputField
          label={t("Repayment")}
          value={repayment.toString()}
          height={32}
          type="number"
          placeholder="0.00"
          onChangeText={(text) => setRepayment(text.toString())}
          style={styles.inputField}
          trailingIcon={<Image source={iconUSDT} style={viewStyles.size12Icon} />}
        />
      </View>
      <View style={styles.rowContainer}>
        <InputField
          label={t("Duration")}
          value={duration.toString()}
          placeholder="0"
          height={32}
          type="number"
          onChangeText={(text) => setDuration(Number(text))}
          style={styles.inputField}
          trailingIcon={
            <Dropdown
              value={durationUnit}
              data={Object.keys(durationUnitMap).map((unit) => ({
                label: t(unit),
                value: unit,
              }))}
              labelField={"label"}
              valueField={"value"}
              onChange={({ value }) => {
                setDurationUnit(value)
              }}
              style={styles.durationDropdown}
              selectedTextStyle={styles.dropdownItem}
              placeholderStyle={styles.dropdownItem}
              itemTextStyle={styles.dropdownItem}
              renderItem={renderDropdownItem}
            />
          }
        />
        <View style={styles.inputField}>
          <LabelView label={"APR"} style={styles.aprLabel} />
          <View style={styles.aprContainer}>
            <Text style={textStyles.MSemiBold}>{apr}</Text>
            <Text style={textStyles.MSemiBold}>%</Text>
          </View>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  formContainer: {
    flexDirection: "column",
    gap: 8,
    marginTop: 16,
  },
  rowContainer: {
    flexDirection: "row",
    alignSelf: "stretch",
    gap: 8,
  },
  inputField: {
    width: "50%",
  },
  aprLabel: {
    marginBottom: 4,
  },
  aprContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    height: 32,
    backgroundColor: Colors.Neutral900,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.Neutral700,
    paddingHorizontal: 8,
    justifyContent: "space-between",
  },
  durationDropdown: {
    height: 38,
    borderColor: Colors.Neutral900,
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: "center",
  },
  dropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
  renderDropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 12,
  },
})

export default LoanDetailsForm
