import React from "react"
import { View, Text, Image, Pressable } from "react-native"
import { useTranslation } from "react-i18next"
import { styles } from "./styles"
import { NFTMortgageItemProps } from "./types"
import { QuantitySelector } from "./QuantitySelector"
import iconBox from "assets/images/ic_box.png"
import { useCreateLoanContext } from "../../context/CreateLoanContext"
import Colors from "@/config/colors"
import { mapEstateToNFTMortgageUI } from "../../types"
import { formatCurrencyByDecimals, formatCurrencyByDecimals2Number } from "@/utils"
import { useCurrencies } from "@/screens/shared/hooks/useCurrencies"

const NFTMortgageItem: React.FC<NFTMortgageItemProps> = ({ item }) => {
  const { t } = useTranslation()
  const nftMortgageUI = mapEstateToNFTMortgageUI(item)

  const { id, imageUrl, propertyTitle, unitPrice, balance, decimals, currency } = nftMortgageUI
  const { selectedNFT, setSelectedNFT, quantity, setQuantity } = useCreateLoanContext()
  const isSelected = selectedNFT?.id === id

  const totalQuantity = formatCurrencyByDecimals2Number(balance, decimals) || 0
  const selectQuantity = (isSelected ? Number(quantity) : 0) || 0
  const formatedUnitPrice = formatCurrencyByDecimals(unitPrice, decimals)
  const tokenSymbol = useCurrencies(currency).tokenSymbol

  const handlePress = () => {
    if (isSelected) return
    setSelectedNFT(item)
    setQuantity(0)
  }

  const handleQuantityChange = (value: number) => {
    setQuantity(value)
  }
  return (
    <Pressable
      style={[styles.lendingCard, isSelected && styles.selectedLendingCard]}
      onPress={handlePress}
    >
      <View style={styles.topSection}>
        <Image style={styles.propertyImage} source={{ uri: imageUrl }} resizeMode="cover" />
        <View style={styles.textDetails}>
          <Text
            style={[styles.propertyTitle, isSelected && { color: Colors.PalleteWhite }]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {propertyTitle}
          </Text>
          <View style={styles.indicators}>
            <View style={styles.priceIndicator}>
              <Image style={styles.iconContainer} source={iconBox} />

              <Text
                style={[styles.priceText, isSelected && { color: Colors.Neutral300 }]}
              >{`${formatedUnitPrice} ${tokenSymbol} / NFT`}</Text>
            </View>
          </View>
        </View>
      </View>
      <View style={[styles.bottomSection, isSelected && { borderColor: Colors.Primary800 }]}>
        <QuantitySelector
          disabled={!isSelected}
          selected={isSelected}
          value={selectQuantity}
          max={totalQuantity}
          onChange={handleQuantityChange}
        />
        <View
          style={[styles.ownedQuantity, isSelected && { backgroundColor: Colors.PalleteBlack }]}
        >
          <Text style={styles.ownedQuantityText}>
            {t("quantityOwned", { count: totalQuantity })}
          </Text>
        </View>
      </View>
    </Pressable>
  )
}

export default NFTMortgageItem
