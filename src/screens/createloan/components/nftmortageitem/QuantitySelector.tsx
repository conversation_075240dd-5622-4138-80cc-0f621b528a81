import React, { useState } from "react"
import { View, Text, Pressable, TextInput, StyleSheet } from "react-native"
import { QuantitySelectorProps } from "./types"
import { styles } from "./styles"
import Colors from "@/config/colors"

export const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  value,
  max,
  min = 0,
  onChange,
  disabled = false,
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [inputValue, setInputValue] = useState(value.toString())

  const handleDecrease = () => {
    if (!disabled && value > min) {
      onChange(value - 1)
    }
  }

  const handleIncrease = () => {
    if (!disabled && value < max) {
      onChange(value + 1)
    }
  }

  const handleInputChange = (text: string) => {
    // Allow only numbers
    if (!disabled && /^\d*$/.test(text)) {
      setInputValue(text)
    }
  }

  const handleInputBlur = () => {
    setIsEditing(false)
    const newValue = parseInt(inputValue, 10) || 0
    if (newValue > max) {
      onChange(max)
      setInputValue(max.toString())
    } else if (newValue < min) {
      onChange(min)
      setInputValue(min.toString())
    } else {
      onChange(newValue)
      setInputValue(newValue.toString())
    }
  }

  React.useEffect(() => {
    setInputValue(value.toString())
  }, [value])

  return (
    <View style={[styles.quantitySelector, disabled && localStyles.disabled]}>
      <Pressable
        style={[
          styles.quantityButton,
          styles.minusButton,
          (value === min || disabled) && localStyles.disabledButton,
        ]}
        onPress={handleDecrease}
        disabled={value === min || disabled}
      >
        <View style={localStyles.minusLine} />
      </Pressable>

      {isEditing && !disabled ? (
        <TextInput
          style={styles.quantityInput}
          value={inputValue}
          onChangeText={handleInputChange}
          onBlur={handleInputBlur}
          keyboardType="number-pad"
          selectTextOnFocus
          autoFocus
          maxLength={String(max).length}
          editable={!disabled}
        />
      ) : (
        <Pressable onPress={() => !disabled && setIsEditing(true)}>
          <Text style={styles.quantityText}>{value}</Text>
        </Pressable>
      )}

      <Pressable
        style={[styles.quantityButton, (value === max || disabled) && localStyles.disabledButton]}
        onPress={handleIncrease}
        disabled={value === max || disabled}
      >
        <View style={localStyles.plusVerticalLine} />
        <View style={localStyles.plusHorizontalLine} />
      </Pressable>
    </View>
  )
}

const localStyles = StyleSheet.create({
  disabled: {
    opacity: 0.5,
  },
  disabledButton: {
    opacity: 0.3,
    borderColor: Colors.Neutral800,
    backgroundColor: Colors.Neutral700,
  },
  minusLine: {
    width: 8,
    height: 1,
    backgroundColor: Colors.PalleteWhite,
  },
  plusVerticalLine: {
    width: 1,
    height: 8,
    backgroundColor: Colors.PalleteWhite,
  },
  plusHorizontalLine: {
    width: 8,
    height: 1,
    backgroundColor: Colors.PalleteWhite,
    position: "absolute",
  },
})

export default QuantitySelector
