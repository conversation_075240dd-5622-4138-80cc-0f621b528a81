import { PublicEstate } from "@/service/types"

export interface NFTMortgageItemProps {
  item: PublicEstate
}

export interface QuantitySelectorProps {
  value: number
  max: number
  min?: number
  onChange: (value: number) => void
  disabled?: boolean
  selected?: boolean
}

export interface PriceIndicatorProps {
  price: string
  unit?: string
}

export interface APRIndicatorProps {
  apr: number
}

export interface QuantityOwnedProps {
  quantity: number
}
