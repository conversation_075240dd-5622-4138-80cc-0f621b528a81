import { StyleSheet, ViewStyle, TextStyle, ImageStyle } from "react-native"
import Colors from "@/config/colors"
import { textStyles } from "@/config/styles"

export const styles = StyleSheet.create({
  lendingCard: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "flex-start",
    padding: 0,
    height: 205.88,
    backgroundColor: Colors.Neutral950,
    borderRadius: 8,
    flexGrow: 1,
    borderWidth: 0,
    overflow: "hidden", // For box-sizing equivalent in React Native
  } as ViewStyle,

  selectedLendingCard: {
    backgroundColor: Colors.Primary900, // #243027
    borderWidth: 1,
    borderColor: Colors.Primary300, // #B2F2C2
  } as ViewStyle,

  topSection: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 6,
    gap: 6,
    height: 145.88,
    width: "100%",
    alignSelf: "stretch",
  } as ViewStyle,

  propertyImage: {
    height: 97.88,
    borderRadius: 6,
    alignSelf: "stretch",
  } as ImageStyle,

  textDetails: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 0,
    gap: 8,
    height: 30,
    alignSelf: "stretch",
  } as ViewStyle,

  propertyTitle: {
    ...textStyles.SMedium,
    letterSpacing: -0.4,
    color: Colors.PalleteWhite,
    alignSelf: "stretch",
  } as TextStyle,

  indicators: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 6,
    width: 113,
    height: 10,
  } as ViewStyle,

  priceIndicator: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 2,
    height: 10,
  } as ViewStyle,

  iconContainer: {
    width: 8,
    height: 8,
  },

  priceText: {
    ...textStyles.XSMedium,
    letterSpacing: -0.32,
    color: Colors.Neutral300, // #BABABC
  } as TextStyle,

  separator: {
    width: 2,
    height: 2,
    backgroundColor: Colors.Neutral500,
    borderRadius: 1,
  } as ViewStyle,

  aprIndicator: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 2,
    width: 43,
    height: 10,
  } as ViewStyle,

  aprText: {
    ...textStyles.XSMedium,
    width: 33,
    letterSpacing: -0.32,
    color: Colors.Neutral300, // #BABABC
  } as TextStyle,

  bottomSection: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "flex-start",
    padding: 6,
    gap: 6,
    height: 60,
    borderTopWidth: 1,
    borderColor: Colors.Neutral900,
    alignSelf: "stretch",
  } as ViewStyle,

  quantitySelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 0,
    height: 24,
    backgroundColor: Colors.PalleteBlack80,
    borderRadius: 4,
    alignSelf: "stretch",
  } as ViewStyle,

  quantityButton: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: 24,
    height: 24,
    backgroundColor: Colors.Primary700,
    borderWidth: 1,
    borderColor: Colors.Primary800,
    borderRadius: 4,
  } as ViewStyle,

  minusButton: {
    opacity: 0.5,
  } as ViewStyle,
  quantityText: {
    ...textStyles.SMedium,
    textAlign: "center",
    letterSpacing: -0.04,
    color: Colors.PalleteWhite,
    alignSelf: "center",
  } as TextStyle,
  quantityInput: {
    ...textStyles.SMedium,
    width: 40,
    textAlign: "center",
    letterSpacing: -0.04,
    color: Colors.PalleteWhite,
    padding: 0,
    alignSelf: "center",
  } as TextStyle,
  ownedQuantity: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 10,
    height: 18,
    backgroundColor: Colors.PalleteBlack, // #17181E
    borderRadius: 4,
    alignSelf: "stretch",
  } as ViewStyle,

  ownedQuantityText: {
    ...textStyles.XSMedium,
    letterSpacing: -0.04,
    color: Colors.Neutral300, // #BABABC
    textAlign: "center",
  } as TextStyle,
})
