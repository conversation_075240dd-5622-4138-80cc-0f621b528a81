import React from "react"
import { View, StyleSheet, Dimensions, Text } from "react-native"
import NFTMortgageItem from "./nftmortageitem/NFTMortgageItem"
import { textStyles } from "@/config/styles"
import Colors from "@/config/colors"
import { useTranslation } from "react-i18next"
import { getEstatesSearchApi } from "@/service"
import SimplePagingList from "@/components/simplepaginglist/SimplePagingList"
import { PublicEstate } from "@/service/types"
import useAuthStore from "@/stores/authStore"

const COLUMN_COUNT = 2
const MAX_ITEMS = 6

const ListNFTsMortgage: React.FC = () => {
  const { t } = useTranslation()
  const { address } = useAuthStore()

  const renderHeader = () => {
    return <Text style={[textStyles.MMedium, styles.headerText]}>{t("Select NFTs to Lend")}</Text>
  }

  const renderItem = (item: PublicEstate) => {
    return (
      <View style={styles.itemContainer}>
        <NFTMortgageItem item={item} />
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {renderHeader()}
      <SimplePagingList<PublicEstate>
        getData={(params) => getEstatesSearchApi({ ...params, balanceOf: address })}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
        initialPage={1}
        pageSize={MAX_ITEMS}
        numColumns={COLUMN_COUNT}
        columnWrapperStyle={styles.columnWrapper}
        contentContainerStyle={styles.contentContainer}
        defaultHeight={500}
      />
    </View>
  )
}

const { width } = Dimensions.get("window")
const styles = StyleSheet.create({
  container: {
    padding: 8,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    borderRadius: 12,
  },
  columnWrapper: {
    justifyContent: "space-between",
    gap: 8,
  },
  contentContainer: {
    gap: 8,
    marginBottom: 8,
  },
  itemContainer: {
    width: (width - 48) / 2,
  },
  headerText: {
    color: Colors.Neutral500,
    marginBottom: 8,
  },
})

export default ListNFTsMortgage
