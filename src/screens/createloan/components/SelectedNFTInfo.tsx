import React from "react"
import { useTranslation } from "react-i18next"
import { View, Text, StyleSheet, Image, StyleProp, ViewStyle } from "react-native"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import iconUsdt from "assets/images/ic_usdt.png"
import iconBox from "assets/images/ic_box.png"
import iconInfo from "assets/images/ic_info.png"
import iconArrowRight from "assets/images/ic_arrow_right.png"
import iconFolderX from "assets/images/ic_folder_x.png"
import { useCreateLoanContext } from "../context/CreateLoanContext"
import { PrimaryButton } from "@/components/Button"
import { mapEstateToNFTMortgageUI } from "../types"
import { useMortgageNFT } from "../hooks/useMortgageNFT"

interface SelectedNFTInfoProps {
  style?: StyleProp<ViewStyle>
}

const SelectedNFTInfo: React.FC<SelectedNFTInfoProps> = ({ style }) => {
  const { t } = useTranslation()
  const { selectedNFT, quantity, principal, repayment, duration, durationUnit, apr } =
    useCreateLoanContext()
  const formattedPrincipal = principal
  const formattedRepayment = repayment
  const formattedQuantity = quantity?.toLocaleString()
  const formattedDuration = duration
  const enabled = Boolean(
    selectedNFT && quantity && principal && repayment && duration && durationUnit,
  )

  const durationUnitDisplay = duration > 1 ? t(durationUnit + "s") : t(durationUnit)

  const { onSubmit } = useMortgageNFT()

  const onMortgageNFT = () => {
    if (!selectedNFT || !quantity) return
    onSubmit(selectedNFT, quantity, principal, repayment, duration, durationUnit)
  }

  const selectedNFTUI = selectedNFT && mapEstateToNFTMortgageUI(selectedNFT)

  return (
    <View style={[styles.container, style]}>
      <View style={styles.headerContainer}>
        <Text style={[styles.previewText, textStyles.XLMedium]}>{t("Preview")}</Text>
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.cardContainer}>
          <View style={styles.nftDetailsContainer}>
            {selectedNFT ? (
              <View style={styles.nftHeaderContainer}>
                <Image
                  style={styles.nftImage}
                  source={{ uri: selectedNFTUI?.imageUrl }}
                  resizeMode="cover"
                />
                <View style={styles.nftInfoContainer}>
                  <Text style={[styles.nftTitleText, textStyles.MMedium]} numberOfLines={4}>
                    {selectedNFTUI?.propertyTitle}
                  </Text>
                  <View style={styles.priceContainer}>
                    <Image style={viewStyles.size10Icon} source={iconBox} resizeMode="contain" />
                    <Text style={[styles.nftPriceText, textStyles.XSMedium]}>
                      {`$${(selectedNFTUI?.unitPrice || 0).toLocaleString()} / NFT`}
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View style={styles.noNFTContainer}>
                <Image source={iconFolderX} style={styles.noNFTIcon} resizeMode="contain" />
                <Text style={[textStyles.SMedium, styles.noNFTText]}>{t("No NFT Selected")}</Text>
              </View>
            )}

            <View style={styles.divider} />

            <View style={styles.loanDetailsContainer}>
              <View style={styles.detailRow}>
                <Text style={[styles.labelText, textStyles.SMedium]}>{t("Loan Amount")}</Text>
                <View style={styles.valueContainer}>
                  <Text style={[styles.valueText, textStyles.SMedium]}>{formattedPrincipal}</Text>
                  <Image style={viewStyles.size14Icon} source={iconUsdt} resizeMode="contain" />
                </View>
              </View>

              <View style={styles.detailRow}>
                <Text style={[styles.labelText, textStyles.SMedium]}>{t("APR")}</Text>
                <View style={styles.valueContainerSmall}>
                  <Text style={[styles.valueText, textStyles.SMedium]}>{apr}</Text>
                  <Text style={[styles.percentageText, textStyles.LMedium]}>{t("%")}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <Text style={[styles.labelText, textStyles.SMedium]}>{t("Duration")}</Text>
                <View style={styles.valueContainerSmall}>
                  <Text style={[styles.valueText, textStyles.SMedium]}>{formattedDuration}</Text>
                  <Text style={[styles.valueText, textStyles.SMedium]}>{durationUnitDisplay}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <Text style={[styles.labelText, textStyles.SMedium]}>{t("NFT Collateral")}</Text>
                <View style={styles.valueContainerSmall}>
                  <Text style={[styles.valueText, textStyles.SMedium]}>{formattedQuantity}</Text>
                  <Text style={[styles.valueText, textStyles.SMedium]}>{t("NFTs")}</Text>
                </View>
              </View>
            </View>

            <View style={styles.divider} />

            <View style={styles.detailRow}>
              <Text style={[styles.labelText, textStyles.MMedium]}>{t("Total Repayment")}</Text>
              <View style={styles.valueContainerLarge}>
                <Text style={[styles.valueText, textStyles.MMedium]}>{formattedRepayment}</Text>
                <Image style={viewStyles.size16Icon} source={iconUsdt} resizeMode="contain" />
              </View>
            </View>
          </View>
        </View>

        <View style={styles.infoContainer}>
          <Image style={viewStyles.size16Icon} source={iconInfo} resizeMode="contain" />
          <Text style={[styles.infoText, textStyles.SSemiBold]}>
            {t(
              "By creating this loan, you agree to the terms and conditions of Briky Lend. Your NFT will be used as collateral until the loan is fully repaid.",
            )}
          </Text>
        </View>
      </View>

      <PrimaryButton
        title={t("Borrow & Create Loan")}
        onPress={onMortgageNFT}
        color={Colors.Success300}
        contentColor={Colors.PalleteBlack}
        textStyle={textStyles.LMedium}
        width={"100%"}
        height={32}
        enabled={enabled}
        trailingIcon={
          <Image
            style={viewStyles.size16Icon}
            source={iconArrowRight}
            resizeMode="contain"
            tintColor={Colors.PalleteBlack}
          />
        }
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 0,
    gap: 16,
    marginTop: 16,
    alignSelf: "stretch",
  },
  headerContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 0,
    gap: 8,
  },
  previewText: {
    color: Colors.PalleteWhite,
    alignSelf: "stretch",
  },
  contentContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 0,
    gap: 8,
    alignSelf: "stretch",
  },
  cardContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 8,
    gap: 8,
    backgroundColor: Colors.Neutral950,
    borderRadius: 6,
    alignSelf: "stretch",
  },
  nftDetailsContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 0,
    gap: 8,
    alignSelf: "stretch",
  },
  nftHeaderContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 8,
  },
  nftImage: {
    width: 40,
    height: 40,
    borderRadius: 4,
  },
  nftInfoContainer: {
    flexDirection: "column",
    padding: 0,
    gap: 4,
    flexShrink: 1,
  },
  nftTitleText: {
    color: Colors.PalleteWhite,
    alignSelf: "stretch",
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 2,
  },
  nftPriceText: {
    alignItems: "center",
    color: Colors.Neutral300,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.Neutral900,
    alignSelf: "stretch",
  },
  loanDetailsContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 0,
    gap: 8,
    alignSelf: "stretch",
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 0,
    gap: 6,
    alignSelf: "stretch",
  },
  labelText: {
    color: Colors.Neutral500,
  },
  valueContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 4,
  },
  valueContainerSmall: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 2,
  },
  valueContainerLarge: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 6,
  },
  valueText: {
    alignItems: "center",
    color: Colors.PalleteWhite,
  },
  percentageText: {
    alignItems: "center",
    color: Colors.PalleteWhite,
  },
  infoContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 6,
    gap: 6,
    backgroundColor: Colors.Neutral900,
    borderRadius: 4,
    alignSelf: "stretch",
  },
  infoText: {
    color: Colors.Neutral500,
    flexShrink: 1,
  },
  lendButton: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 12,
    gap: 6,
    height: 32,
    backgroundColor: Colors.Success300,
    borderRadius: 6,
    alignSelf: "stretch",
  },
  buttonText: {
    alignItems: "center",
    textAlign: "center",
    color: Colors.PalleteBlack,
  },
  noNFTContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 16,
    gap: 6,
    height: 40,
    backgroundColor: "#2F3035",
    borderRadius: 4,
    alignSelf: "stretch",
  },
  noNFTIcon: {
    width: 16,
    height: 16,
  },
  noNFTText: {
    color: Colors.Neutral500,
  },
})

export default SelectedNFTInfo
