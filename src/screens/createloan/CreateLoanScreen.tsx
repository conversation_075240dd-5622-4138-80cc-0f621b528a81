import * as React from "react"
import { CreateLoanContext } from "./context/CreateLoanContext"
import { useCreateLoan } from "./hooks/useCreateLoan"
import CreateLoanView from "./CreateLoanView"

const CreateLoanScreen = () => {
  const { contextValue } = useCreateLoan()
  return (
    <CreateLoanContext.Provider value={contextValue}>
      <CreateLoanView />
    </CreateLoanContext.Provider>
  )
}

export default CreateLoanScreen
