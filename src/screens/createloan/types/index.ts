import { PublicEstate } from "@/service/types"

export interface NFTMortgageUI {
  id: string
  imageUrl: string
  propertyTitle: string
  unitPrice: string
  balance: string
  decimals: number
  currency: string
}

export function mapEstateToNFTMortgageUI(estate: PublicEstate): NFTMortgageUI {
  return {
    id: estate.id,
    imageUrl: estate.imageUrl,
    propertyTitle: estate?.name,
    unitPrice: estate?.initialUnitPrice || "18000000000000000000",
    balance: estate?.balance || "0",
    decimals: estate.decimals,
    currency: estate.currency || "0x1a2c6468cbac082f8fc4d00599433841e7ced610",
  }
}
