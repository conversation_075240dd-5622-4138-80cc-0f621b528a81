// siweConfig.ts

import {
  createSIWEConfig,
  type SIWECreateMessageArgs,
  SIWESession,
  type SIWEVerifyMessageArgs,
} from "@reown/appkit-siwe-react-native"
import { getProfile<PERSON>pi, getNonce<PERSON><PERSON>, login<PERSON>pi } from "@/service"
import { SignInResponse, User } from "@/service/types"
import { clearStoredToken, getStoredToken } from "@/utils/storage"
import { PROVIDER_CHAIN_ID } from "@/config/env"
import { retry } from "@/utils/promise"
import Logger from "@/utils/logger"

const logger = new Logger({ tag: "Auth" })

export const WELCOME_MESSAGE = (address: string, nonce: string) =>
  `Welcome to Briky Land!

Click to sign in and accept the Briky Land Terms of Service (https://brikyland.com/tos) and Privacy Policy (https://brikyland.com/privacy).

This request will not trigger a blockchain transaction or cost any gas fees.

Wallet address:
${address}

Nonce:
${nonce}`

export function getAddressAndNonceFromMessage(message: string): {
  address: string
  nonce: string
} {
  // Tách message thành các dòng và loại bỏ khoảng trắng thừa
  const lines = message.split("\n").map((line) => line.trim())

  // Tìm vị trí của các nhãn "Wallet address:" và "Nonce:"
  const addressLabelIndex = lines.findIndex((line) => line === "Wallet address:")
  const nonceLabelIndex = lines.findIndex((line) => line === "Nonce:")

  if (addressLabelIndex === -1 || nonceLabelIndex === -1) {
    throw new Error("Không tìm thấy nhãn 'Wallet address:' hoặc 'Nonce:' trong message")
  }

  // Lấy địa chỉ ví là dòng không rỗng ngay sau nhãn "Wallet address:"
  let address = ""
  for (let i = addressLabelIndex + 1; i < lines.length; i++) {
    if (lines[i] !== "") {
      address = lines[i]
      break
    }
  }

  // Lấy nonce là dòng không rỗng ngay sau nhãn "Nonce:"
  let nonce = ""
  for (let i = nonceLabelIndex + 1; i < lines.length; i++) {
    if (lines[i] !== "") {
      nonce = lines[i]
      break
    }
  }

  if (!address || !nonce) {
    throw new Error("Không tìm thấy địa chỉ ví hoặc nonce trong message")
  }

  return { address, nonce }
}

export const siweConfig = (
  updateProfile: (profile: User) => void,
  updateAddress: (address: string) => void,
) =>
  createSIWEConfig({
    getNonce: async (address?: string): Promise<string> => {
      if (!address) throw new Error("Address is required")
      return await getNonceApi(address)
    },
    verifyMessage: async ({ message, signature }: SIWEVerifyMessageArgs): Promise<boolean> => {
      try {
        const { address, nonce } = getAddressAndNonceFromMessage(message)
        logger.debug("Verifying message signature", {
          address,
          nonce,
          signature,
        })

        await retry<SignInResponse | null>(() => loginApi(address, nonce, signature), 3, 500)
        const userProfile = await retry<User>(() => getProfileApi(address), 3, 500)
        updateProfile(userProfile!)
        updateAddress(address)
        return true
      } catch (error) {
        logger.error("Failed to verify message signature", error)
        return false
      }
    },
    getSession: async (): Promise<SIWESession | null> => {
      const { address } = await getStoredToken()
      if (!address) throw new Error("Address is required")
      return { address, chainId: PROVIDER_CHAIN_ID }
    },
    signOut: async (): Promise<boolean> => {
      await clearStoredToken()
      return true
    },
    createMessage: ({ address, ...args }: SIWECreateMessageArgs): string => {
      // Method for generating an EIP-4361-compatible message.
      const walletAddress = address.split(":").pop() || ""
      const message = WELCOME_MESSAGE(walletAddress, args.nonce)
      logger.debug("Created authentication message", { message })
      return message
    },
    nonceRefetchIntervalMs: 1000 * 60 * 60 * 14,
    sessionRefetchIntervalMs: 1000 * 60 * 60 * 14,
    signOutOnDisconnect: false,
    // Defaults to true
    signOutOnAccountChange: false,
    // Defaults to true
    signOutOnNetworkChange: false,
  })
