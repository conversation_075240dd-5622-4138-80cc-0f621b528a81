/**
 * Logger configuration for the application
 * This file sets up the logger for different parts of the application
 */

import { logger } from "./logger"

/**
 * Initialize the logger for the application
 * This should be called early in the application lifecycle
 */
export const initializeLogger = () => {
  // Enable logging in development environment
  const isDevelopment = process.env.NODE_ENV !== "production"
  logger.setEnabled(isDevelopment)

  // Log initialization
  logger.info("Logger initialized", {
    environment: process.env.NODE_ENV,
    loggingEnabled: isDevelopment,
  })
}

/**
 * Create a class-specific logger
 *
 * Usage example:
 * ```
 * class UserService {
 *   private logger = new Logger({ tag: "UserService" });
 *
 *   constructor() {
 *     this.logger.info('UserService initialized');
 *   }
 * }
 * ```
 */

/**
 * Filter logs to only show specific tags
 * @param tags Array of tags to enable, or null to show all tags
 */
export const filterLogsByTags = (tags?: string[]) => {
  logger.setEnabledTags(tags)
}

/**
 * Enable or disable logging
 * @param enabled Whether logging should be enabled
 */
export const setLoggingEnabled = (enabled: boolean) => {
  logger.setEnabled(enabled)
}
