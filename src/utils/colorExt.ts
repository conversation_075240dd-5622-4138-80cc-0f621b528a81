const convertHexToRGBA = (hex: string, alpha: number): string => {
  const sanitizedHex = hex.replace("#", "")

  if (sanitizedHex.length !== 6) {
    throw new Error("Invalid hex length")
  }

  const r = parseInt(sanitizedHex.substring(0, 2), 16)
  const g = parseInt(sanitizedHex.substring(2, 4), 16)
  const b = parseInt(sanitizedHex.substring(4, 6), 16)

  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

export { convertHexToRGBA }
