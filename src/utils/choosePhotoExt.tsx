import { ImagePickerAsset, ImagePickerOptions } from "expo-image-picker/src/ImagePicker.types"
import { Alert, Linking } from "react-native"
import * as ImagePicker from "expo-image-picker"
import { useTranslation } from "react-i18next"
import { useActionSheet } from "@expo/react-native-action-sheet"
import { TFunction } from "i18next"
import Logger from "./logger"

const logger = new Logger({ tag: "ChoosePhotoExt" })

export const getPhotoFileName = (fileName: string | null, uri: string) => {
  return fileName || uri.split("/").pop() || ""
}

export const getImageType = (fileName: string) => {
  const extension = fileName.split(".").pop()?.toLowerCase() || "jpeg"

  const mimeTypes: Record<string, string> = {
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    png: "image/png",
    gif: "image/gif",
    bmp: "image/bmp",
    webp: "image/webp",
    svg: "image/svg+xml",
  }

  return mimeTypes[extension] || "image/jpeg"
}

export const useChoosePhoto = () => {
  const { t } = useTranslation()
  const actionSheetProps = useActionSheet()

  const handleChoosePhoto = (
    onSuccess: (files: ImagePickerAsset[]) => void,
    imagePickerOptions?: ImagePickerOptions & {
      allowsMultipleSelection?: boolean
    },
  ) => {
    const { showActionSheetWithOptions } = actionSheetProps

    const options = [t("Take Photo"), t("Choose from Library"), t("Cancel")]
    const cancelButtonIndex = 2
    showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex,
      },
      async (buttonIndex) => {
        if (buttonIndex === 0) {
          const { status } = await ImagePicker.requestCameraPermissionsAsync()
          if (status !== "granted") {
            Alert.alert(t("Request permission"), t("You need to grant camera access"), [
              {
                text: t("Cancel"),
                style: "cancel",
              },
              {
                text: t("Open settings"),
                onPress: () => {
                  Linking.openSettings()
                },
              },
            ])
            return
          }
          const result = await ImagePicker.launchCameraAsync({
            mediaTypes: ["images"],
            allowsEditing: false,
            aspect: [4, 3],
            quality: 1,
          })

          if (!result.canceled) {
            onSuccess([result.assets[0]])
          }
        } else if (buttonIndex === 1) {
          onChoosePhotoFromGallery(onSuccess, imagePickerOptions?.allowsMultipleSelection, t)
        }
      },
    )
  }

  return { handleChoosePhoto, getPhotoFileName, getImageType }
}

export const onChoosePhotoFromGallery = async (
  onSuccess: (files: ImagePickerAsset[]) => void,
  allowMultipleSelection: boolean = false,
  t: TFunction<any, any>,
) => {
  try {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync()
    if (status !== "granted") {
      Alert.alert(t("Request permission"), t("You need to grant access to the photo library"), [
        {
          text: t("Cancel"),
          style: "cancel",
        },
        {
          text: t("Open settings"),
          onPress: () => {
            Linking.openSettings()
          },
        },
      ])
      return
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: false,
      aspect: [4, 3],
      quality: 1,
      allowsMultipleSelection: allowMultipleSelection,
    })

    if (!result.canceled) {
      onSuccess(result.assets)
    }
  } catch (error) {
    logger.error("Error choosing photo from gallery", error)
  }
}
