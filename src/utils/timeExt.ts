import { TFunction } from "i18next"

const durationUnitMap: Record<string, number> = {
  minute: 60,
  hour: 60 * 60,
  day: 60 * 60 * 24,
  week: 60 * 60 * 24 * 7,
  month: 60 * 60 * 24 * 30,
}

const LAST_SECOND_TIME_STAMP_2025 = 1767225599

const HOURS_PER_DAY = 24
const MINUTES_PER_HOUR = 60
const SECONDS_PER_MINUTE = 60
// TODO: check Leap Year has 366 days
const DAYS_PER_YEAR = 365

const SECONDS_PER_YEAR = DAYS_PER_YEAR * HOURS_PER_DAY * MINUTES_PER_HOUR * SECONDS_PER_MINUTE
const SECONDS_PER_DAY = HOURS_PER_DAY * MINUTES_PER_HOUR * SECONDS_PER_MINUTE
const SECONDS_PER_HOUR = MINUTES_PER_HOUR * SECONDS_PER_MINUTE

const getWeekDays = (t: TFunction) => {
  return [
    t("Sunday"),
    t("Monday"),
    t("Tuesday"),
    t("Wednesday"),
    t("Thursday"),
    t("Friday"),
    t("Saturday"),
  ]
}

function calculateTimeDifference(startDate: Date, endDate: Date): string {
  let delta = endDate.getTime() - startDate.getTime()

  const days = Math.floor(delta / (1000 * 60 * 60 * 24))
  delta -= days * (1000 * 60 * 60 * 24)

  const hours = Math.floor(delta / (1000 * 60 * 60))
  delta -= hours * (1000 * 60 * 60)

  const minutes = Math.floor(delta / (1000 * 60))
  delta -= minutes * (1000 * 60)

  const seconds = Math.floor(delta / 1000)

  return `${days}d ${hours}h ${minutes}m ${seconds}s`
}

function convertSecondsToTime(seconds: number): string {
  if (seconds < 0) throw new Error("Duration cannot be negative.")

  const units = [
    { label: "year", seconds: 365 * 24 * 60 * 60 },
    { label: "month", seconds: 30 * 24 * 60 * 60 },
    { label: "day", seconds: 24 * 60 * 60 },
    { label: "hour", seconds: 60 * 60 },
    { label: "minute", seconds: 60 },
    { label: "second", seconds: 1 },
  ]

  for (const unit of units) {
    const value = Math.floor(seconds / unit.seconds)
    if (value > 0) {
      return value === 1 ? `1 ${unit.label}` : `${value} ${unit.label}s`
    }
  }

  return "0 second"
}

enum DateTimeFormat {
  HHMM,
  SHORT,
  LONG,
}

function convertDateFromTimeStamp(
  timestamp: number,
  formatType: DateTimeFormat = DateTimeFormat.SHORT,
): string {
  const date = new Date(timestamp)
  const hours = date.getHours().toString().padStart(2, "0")
  const minutes = date.getMinutes().toString().padStart(2, "0")
  const day = date.getDate().toString().padStart(2, "0")
  const month = (date.getMonth() + 1).toString().padStart(2, "0")
  const year = date.getFullYear()

  switch (formatType) {
    case DateTimeFormat.HHMM:
      return `${hours}:${minutes} - ${day}/${month}/${year}`
    case DateTimeFormat.SHORT:
      return `${day}/${month}/${year}`
    case DateTimeFormat.LONG:
      return `${day}/${month}/${year} ${hours}:${minutes}`
    default:
      return `${hours}:${minutes}, ${day}/${month}/${year}`
  }
}

function convertTime(time: string, t: TFunction): string {
  const weekDays = getWeekDays(t)
  const date = new Date(time)
  const dayOfWeek = date.getDay()

  const day = date.getDate()
  const month = date.getMonth() + 1
  const year = date.getFullYear()
  return `${weekDays[dayOfWeek]} - ${day}/${month}/${year}`
}

function formatShortTime(time: string): string {
  const date = new Date(time)
  const day = date.getDate()
  const month = date.getMonth()
  const year = date.getFullYear()
  return `${day}/${month}/${year}`
}

function getTimePartsFromDuration(time: number) {
  const days = Math.floor(time / (MINUTES_PER_HOUR * SECONDS_PER_MINUTE * HOURS_PER_DAY))
  const hours = Math.floor(
    (time % (HOURS_PER_DAY * MINUTES_PER_HOUR * SECONDS_PER_MINUTE)) /
      (MINUTES_PER_HOUR * SECONDS_PER_MINUTE),
  )
  const minutes = Math.floor((time % (MINUTES_PER_HOUR * SECONDS_PER_MINUTE)) / SECONDS_PER_MINUTE)
  const seconds = time % SECONDS_PER_MINUTE

  return {
    days: String(days).padStart(2, "0"),
    hours: String(hours).padStart(2, "0"),
    minutes: String(minutes).padStart(2, "0"),
    seconds: String(seconds).padStart(2, "0"),
  }
}

function getRemainingTime(timestamp: number, t: (key: string) => string): string | null {
  if (!timestamp || timestamp <= 0) {
    return null
  }

  const currentTime = Math.floor(Date.now() / 1000)
  const timeDiff = timestamp - currentTime
  if (timeDiff < 0) {
    return null
  }

  if (timeDiff >= SECONDS_PER_YEAR) {
    const years = Math.floor(timeDiff / SECONDS_PER_YEAR)
    return `${years} ${years === 1 ? t("year") : t("years")}`
  }

  if (timeDiff >= SECONDS_PER_DAY) {
    const days = Math.floor(timeDiff / SECONDS_PER_DAY)
    return `${days} ${days === 1 ? t("day") : t("days")}`
  }

  if (timeDiff >= SECONDS_PER_HOUR) {
    const hours = Math.floor(timeDiff / SECONDS_PER_HOUR)
    return `${hours} ${hours === 1 ? t("hour") : t("hours")}`
  }

  if (timeDiff >= SECONDS_PER_MINUTE) {
    const minutes = Math.floor(timeDiff / SECONDS_PER_MINUTE)
    return `${minutes} ${minutes === 1 ? t("minute") : t("minutes")}`
  }

  if (timeDiff < 1) {
    return t("just now")
  }

  return `${timeDiff} ${timeDiff === 1 ? t("second") : t("seconds")}`
}

function getElapsedTime(timestamp: number, t: (key: string) => string): string {
  if (!timestamp || timestamp <= 0) {
    return t("-")
  }

  const currentTime = Math.floor(Date.now() / 1000)
  const timeDiff = currentTime - timestamp

  if (timeDiff < 0) {
    return t("-")
  }

  if (timeDiff < 1) {
    return t("just now")
  }

  if (timeDiff >= SECONDS_PER_YEAR) {
    const years = Math.floor(timeDiff / SECONDS_PER_YEAR)
    return `${years} ${years === 1 ? t("year") : t("years")} ${t("ago")}`
  }

  if (timeDiff >= SECONDS_PER_DAY) {
    const days = Math.floor(timeDiff / SECONDS_PER_DAY)
    return `${days} ${days === 1 ? t("day") : t("days")} ${t("ago")}`
  }

  if (timeDiff >= SECONDS_PER_HOUR) {
    const hours = Math.floor(timeDiff / SECONDS_PER_HOUR)
    return `${hours} ${hours === 1 ? t("hour") : t("hours")} ${t("ago")}`
  }

  if (timeDiff >= SECONDS_PER_MINUTE) {
    const minutes = Math.floor(timeDiff / SECONDS_PER_MINUTE)
    return `${minutes} ${minutes === 1 ? t("minute") : t("minutes")} ${t("ago")}`
  }

  return `${timeDiff} ${timeDiff === 1 ? t("second") : t("seconds")} ${t("ago")}`
}

function formatTimestampToDateTimeString(timestamp: number, t: TFunction): string {
  if (timestamp === 0) return "-"
  // If timestamp is in seconds, convert to ms
  const date = new Date(timestamp * 1000)
  const day = date.getDate()
  const month = date.toLocaleString("en-US", { month: "short" })
  const year = date.getFullYear()
  let hours = date.getHours()
  const minutes = date.getMinutes()
  const ampm = hours >= 12 ? t("PM") : t("AM")
  hours = hours % 12
  hours = hours ? hours : 12 // the hour '0' should be '12'
  const minutesStr = minutes < 10 ? `0${minutes}` : `${minutes}`
  return `${day} ${month}, ${year} / ${hours}:${minutesStr} ${ampm}`
}

function getTimeUpcomingToBeListed(publicSaleEndsAtInSeconds: number): number {
  return Math.ceil(
    (publicSaleEndsAtInSeconds * 1000 + 30 * 24 * 60 * 60 * 1000 - Date.now()) /
      (1000 * 60 * 60 * 24),
  )
}

function getTimeLeftInHHMMSS(timeLeftInSeconds: number): string {
  if (timeLeftInSeconds <= 0) return ""
  const h = Math.floor(timeLeftInSeconds / 3600)
  const m = Math.floor((timeLeftInSeconds % 3600) / 60)
  const s = timeLeftInSeconds % 60
  return `${h.toString().padStart(2, "0")}:${m.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}s`
}

function getDateFromTimestamp(timeStamp?: number): string {
  return timeStamp ? new Date(timeStamp * 1000).toLocaleDateString() : "-"
}

export {
  durationUnitMap,
  calculateTimeDifference,
  convertTime,
  formatShortTime,
  convertSecondsToTime,
  getTimePartsFromDuration,
  getElapsedTime,
  getRemainingTime,
  convertDateFromTimeStamp,
  DateTimeFormat,
  LAST_SECOND_TIME_STAMP_2025,
  getTimeUpcomingToBeListed,
  getTimeLeftInHHMMSS,
  getDateFromTimestamp,
  formatTimestampToDateTimeString,
}
