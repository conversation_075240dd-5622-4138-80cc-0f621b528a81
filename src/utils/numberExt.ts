import Logger from "./logger"
import { formatEther } from "@ethersproject/units"

const logger = new Logger({ tag: "NumberExt" })

function formatToPercent(value: number): string {
  const percentValue = value.toFixed(2)
  return `${percentValue}%`
}

function shortenNumber(value: number): string {
  if (value < 1000) {
    return value.toString()
  } else if (value >= 1000 && value < 1000000) {
    return (value / 1000).toFixed(3).replace(/\.?0+$/, "") + "K"
  } else if (value >= 1000000 && value < 1000000000) {
    return (value / 1000000).toFixed(3).replace(/\.?0+$/, "") + "M"
  } else {
    return (value / 1000000000).toFixed(3).replace(/\.?0+$/, "") + "B"
  }
}

export const formatNumberToReadableFormat = (num: number): string => {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(1).replace(/\.0$/, "") + "B"
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(1).replace(/\.0$/, "") + "M"
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(1).replace(/\.0$/, "") + "K"
  } else {
    return num.toString()
  }
}

const formatNumber = (
  value: number | string | null,
  precision = 4,
  defaultValue: number | string = "-",
  options: Intl.NumberFormatOptions = {},
) => {
  try {
    if (Number.isNaN(value) || value === null) {
      return defaultValue.toString()
    }

    return Intl.NumberFormat("en-US", {
      minimumFractionDigits: 0,
      maximumFractionDigits: precision,
      ...options,
    }).format(+value)
  } catch (error) {
    logger.error("Error formatting number", error)
    return defaultValue.toString()
  }
}

function getFloatValueFromDecimalString(value: string): number {
  const numericString = value.replace(/,/g, "")
  // Convert to a number
  return parseInt(numericString, 10)
}

// Helper function để tính phần trăm với BigInt
function calculatePercentage(sold: string, max: string): number {
  const soldBigInt = BigInt(sold)
  const maxBigInt = BigInt(max)
  if (maxBigInt === BigInt(0)) return 0
  const percentage = (soldBigInt * BigInt(100)) / maxBigInt
  return Number(percentage)
}

function formatNumericByDecimalsToNumber(numeric: string, decimals: number): number {
  const diff = 18 - decimals
  return Number(formatEther((BigInt(numeric) * BigInt(Math.pow(10, diff))).toString()))
}

const calculateApr = (principal: number, repayment: number, durationInSeconds: number) => {
  const secondsInYear = 365 * 24 * 60 * 60
  const repayPrincipalRatio = Number(repayment) / Number(principal) - 1
  const aprValue = repayPrincipalRatio * Number((secondsInYear * 100) / durationInSeconds)

  return isNaN(aprValue) ? "0.0" : aprValue.toFixed(2)
}

export {
  formatToPercent,
  shortenNumber,
  formatNumber,
  getFloatValueFromDecimalString,
  calculatePercentage,
  formatNumericByDecimalsToNumber,
  calculateApr,
}
