export const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

export const retry = async <T>(fn: () => Promise<T>, maxRetries = 3, delayMs = 500) => {
  let attempts = 0
  while (attempts < maxRetries) {
    try {
      return await fn()
    } catch (error) {
      attempts++
      if (attempts >= maxRetries) {
        throw error
      }
      await delay(delayMs)
    }
  }
}
