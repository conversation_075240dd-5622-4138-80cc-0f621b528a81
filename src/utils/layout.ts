import { Dimensions } from "react-native"

const { width: SCREEN_WIDTH } = Dimensions.get("window")
const PADDING = 16
const SPACING = 12

interface GridLayoutOptions {
  numColumns: number
}

export function calculateGridItemWidth({ numColumns }: GridLayoutOptions): number {
  return (SCREEN_WIDTH - PADDING * 2 - SPACING * (numColumns - 1)) / numColumns
}

export function calculateGridItemDimensions({ numColumns }: GridLayoutOptions) {
  const itemWidth = calculateGridItemWidth({ numColumns })

  return {
    itemWidth,
    containerPadding: PADDING,
    itemSpacing: SPACING,
  }
}
