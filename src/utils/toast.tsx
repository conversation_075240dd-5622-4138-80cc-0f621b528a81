import Toast from "react-native-root-toast"
import Colors from "src/config/colors"
import Logger from "./logger"

const logger = new Logger({ tag: "Toast" })

export const showSuccess = (message: string) => {
  logger.success(`Toast: ${message}`)
  Toast.show(message, {
    backgroundColor: Colors.Success500,
    textColor: Colors.PalleteWhite,
    duration: Toast.durations.LONG,
  })
}

export const showError = (message: string, errorCode: string = "") => {
  logger.error(`Toast Error: ${message}`, errorCode ? { errorCode } : undefined)
  Toast.show(message, {
    backgroundColor: Colors.Danger500,
    textColor: Colors.PalleteWhite,
    duration: Toast.durations.LONG,
  })
}

export const showSuccessWhenCallContract = (message: string) => {
  logger.success(`Contract Success: ${message}`)
  Toast.show(message, {
    backgroundColor: Colors.Success500,
    textColor: Colors.PalleteWhite,
    duration: 20_000,
  })
}
