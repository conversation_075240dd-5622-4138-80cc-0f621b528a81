import { useNavigation } from "@react-navigation/native"
import { NativeStackNavigationProp } from "@react-navigation/native-stack"

export const MAIN_TABS = "MainTabs"
export const HOME = "Home"
export const LOAN_DETAIL = "LoanDetail"
export const ALL_LOANS = "AllLoans"
export const LOAN_SALE = "LoanSale"
export const PROFILE = "Profile"
export const SETTING = "Setting"
export const CREATE_LOAN = "CreateLoan"
export const VERIFY_ACCOUNT = "VerifyAccount"
export const EDIT_PROFILE = "EditProfile"
export const REFERENCES = "References"
export const CONTACT = "Contact"
export const OFFICES = "Offices"
export const GUEST_PROFILE = "GuestProfile"

// Legacy object for backward compatibility
export const RouterKeys = {
  MainTabs: MAIN_TABS,
  Home: HOME,
  LoanDetail: LOAN_DETAIL,
  AllLoans: ALL_LOANS,
  LoanSale: LOAN_SALE,
  Profile: PROFILE,
  GuestProfile: GUEST_PROFILE,
  Setting: SETTING,
  CreateLoan: CREATE_LOAN,
  VerifyAccount: VERIFY_ACCOUNT,
  EditProfile: EDIT_PROFILE,
  References: REFERENCES,
  Contact: CONTACT,
  Offices: OFFICES,
}

export type RootStackParamList = {
  MainTabs: undefined
  Home: undefined
  Secondary: undefined
  LoanDetail: { id?: string }
  CreateLoan: undefined
  VerifyAccount: undefined
  EditProfile: undefined
  References: undefined
  Contact: undefined
  Offices: undefined
  GuestProfile: { address: string }
}

export type TabParamList = {
  Home: undefined
  AllLoans: undefined
  LoanSale: undefined
  Profile: undefined
  Setting: undefined
}

export type NavigationProp = NativeStackNavigationProp<RootStackParamList>
export type TabNavigationProp = NativeStackNavigationProp<TabParamList>

export const useAppNavigaton = () => {
  return useNavigation<NavigationProp>()
}

export const useTabNavigaton = () => {
  return useNavigation<TabNavigationProp>()
}
