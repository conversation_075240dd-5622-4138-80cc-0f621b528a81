import * as React from "react"
import { Image, ImageRequireSource } from "react-native"
import { NavigationContainer } from "@react-navigation/native"
import { createStackNavigator } from "@react-navigation/stack"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { useTranslation } from "react-i18next"
import icHome from "assets/images/ic_laptop.png"
import icFileText from "assets/images/ic_file_text.png"
import icFilePlus2 from "assets/images/ic_file_plus_2.png"
import icMenuProfile from "assets/images/ic_user_round.png"
import icSetting from "assets/images/ic_settings.png"
import Colors from "@/config/colors"
import {
  HomeScreen,
  AllLoansScreen,
  LoanSaleScreen,
  ProfileScreen,
  SettingScreen,
  LoanDetailScreen,
  CreateLoanScreen,
  VerifyAccountScreen,
  EditProfileScreen,
  ReferencesScreen,
  ContactScreen,
  OfficesScreen,
} from "@/screens"
import {
  ALL_LOANS,
  LOAN_SALE,
  HOME,
  MAIN_TABS,
  LOAN_DETAIL,
  PROFILE,
  SETTING,
  CREATE_LOAN,
  VERIFY_ACCOUNT,
  EDIT_PROFILE,
  REFERENCES,
  CONTACT,
  OFFICES,
  GUEST_PROFILE,
} from "./Routers"
import { MainTabHeader } from "@/components/MainTabHeader"
import GuestProfileScreen from "@/screens/profile/GuestProfileScreen"

const Stack = createStackNavigator()
const Tab = createBottomTabNavigator()

// Define TabBarIcon component outside of getTabBarIcon function
const TabBarIcon = ({
  color,
  size,
  iconSource,
}: {
  color: string
  size: number
  iconSource: ImageRequireSource
}) => {
  return <Image source={iconSource} style={{ width: size, height: size, tintColor: color }} />
}

// Function to get the TabBarIcon with the correct icon source
const getTabBarIcon = (iconSource: ImageRequireSource) => {
  const IconComponent = ({ color, size }: { color: string; size: number }) => (
    <TabBarIcon color={color} size={size} iconSource={iconSource} />
  )

  // Set display name for the component
  IconComponent.displayName = `TabBarIconWrapper_${iconSource}`

  return IconComponent
}

const MainTabNavigator = () => {
  const { t } = useTranslation()

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: Colors.PalleteWhite,
        tabBarStyle: {
          backgroundColor: Colors.PalleteBlack,
          paddingBottom: 10,
        },
        headerShown: false,
      }}
    >
      <Tab.Screen
        name={HOME}
        component={HomeScreen}
        options={{
          tabBarLabel: t("Home"),
          tabBarIcon: getTabBarIcon(icHome),
        }}
      />
      <Tab.Screen
        name={ALL_LOANS}
        component={AllLoansScreen}
        options={{
          tabBarLabel: t("All Loans"),
          tabBarIcon: getTabBarIcon(icFileText),
        }}
      />
      <Tab.Screen
        name={LOAN_SALE}
        component={LoanSaleScreen}
        options={{
          tabBarLabel: t("Loan Sale"),
          tabBarIcon: getTabBarIcon(icFilePlus2),
        }}
      />
      <Tab.Screen
        name={PROFILE}
        component={ProfileScreen}
        options={{
          tabBarLabel: t("My Profile"),
          tabBarIcon: getTabBarIcon(icMenuProfile),
        }}
      />
      <Tab.Screen
        name={SETTING}
        component={SettingScreen}
        options={{
          tabBarLabel: t("Settings"),
          tabBarIcon: getTabBarIcon(icSetting),
        }}
      />
    </Tab.Navigator>
  )
}

// Define header component outside of AppNavigator
const StackHeader = () => <MainTabHeader />

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName={MAIN_TABS}
        screenOptions={{
          header: StackHeader,
          headerShown: true,
          cardStyle: { backgroundColor: Colors.PalleteBlack },
          animation: "none",
        }}
      >
        <Stack.Screen name={MAIN_TABS} component={MainTabNavigator} />
        <Stack.Screen
          name={LOAN_DETAIL}
          component={LoanDetailScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={CREATE_LOAN}
          component={CreateLoanScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name={GUEST_PROFILE}
          component={GuestProfileScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={VERIFY_ACCOUNT}
          component={VerifyAccountScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={EDIT_PROFILE}
          component={EditProfileScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={REFERENCES}
          component={ReferencesScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={CONTACT}
          component={ContactScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={OFFICES}
          component={OfficesScreen}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  )
}

export default AppNavigator
