module.exports = {
  root: true,
  extends: [
    "@react-native-community",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:@typescript-eslint/recommended",
    "prettier", // <PERSON><PERSON><PERSON> bảo eslint-config-prettier đ<PERSON><PERSON><PERSON> đặt cuối cùng
  ],
  parser: "@typescript-eslint/parser",
  plugins: ["react", "react-hooks", "@typescript-eslint", "prettier", "react-native"],
  rules: {
    "prettier/prettier": "error",
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off", // Không cần import React từ React 17+
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "@typescript-eslint/no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
    "no-console": ["error", { allow: ["warn", "error"] }],
    "@typescript-eslint/no-explicit-any": "off",
    "react/display-name": "off", // Tắt rule yêu cầu display name cho components
    "react-native/no-inline-styles": "error",
  },
  settings: {
    react: {
      version: "detect",
    },
  },
}
