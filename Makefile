run:
	cp .env.staging.sample .env
	npx expo start

generate-i18n:
	npm run generate-i18n

update-version:
	npm run update-version

lint:
	yarn lint:fix

check-format:
	yarn format
	yarn lint:fix
	npx tsc -noEmit

submit-staging:
	cp .env.staging.sample .env
	eas build --platform android --profile staging-release --json --non-interactive --auto-submit-with-profile=staging-release

submit-production:
	cp .env.production.sample .env
	eas build --platform android --profile production --json --non-interactive --auto-submit-with-profile=production