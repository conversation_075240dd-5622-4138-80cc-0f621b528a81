{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "prettier.requireConfig": true,
  "eslint.format.enable": false, // Thêm dòng này để tránh E<PERSON>int ghi đè <PERSON>ttier
  "prettier.jsxSingleQuote": false, // <PERSON><PERSON><PERSON> bảo JSX cũng sử dụng dấu ngoặc kép
  "prettier.singleQuote": false, // Đồng bộ với cấu hình .prettierrc.js
  "prettier.semi": false,
  "makefile.configureOnOpen": false // Đồng bộ với cấu hình .prettierrc.js
}
