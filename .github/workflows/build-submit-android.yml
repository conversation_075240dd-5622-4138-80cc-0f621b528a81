name: Build and Submit Android

on:
  workflow_dispatch:
    inputs:
      profile:
        description: "Chọn profile build & submit"
        required: true
        default: "submit-staging"
        type: choice
        options:
          - submit-staging
          - submit-uat
          - submit-production

jobs:
  build-submit-android:
    runs-on: ubuntu-latest
    env:
      EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Install dependencies
        run: yarn install

      - name: Install EAS CLI
        run: yarn global add eas-cli

      - name: Setup Expo and EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Setup Google Play Service Account Key
        env:
          GOOGLE_PLAY_SERVICE_ACCOUNT: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
        run: |
          echo "$GOOGLE_PLAY_SERVICE_ACCOUNT" > service-account.json
        # Đả<PERSON> bảo đường dẫn file khớp với cấu hình serviceAccountKeyPath trong eas.json

      - name: Build and Submit Android App
        run: |
          echo "Sử dụng profile: ${{ github.event.inputs.profile }}"
          make ${{ github.event.inputs.profile }}

      - name: Cleanup sensitive files
        if: always()
        run: |
          rm -f service-account.json
