name: Update Version and Create PR

on:
  workflow_dispatch:
    inputs:
      major:
        description: "Major version"
        required: true
        default: "1"
      minor:
        description: "Minor version"
        required: true
        default: "0"
      patch:
        description: "Patch version"
        required: true
        default: "0"

jobs:
  update-version:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Update version.ts
        run: |
          echo "export const MAJOR_VERSION = ${{ github.event.inputs.major }};" > version.ts
          echo "export const MINOR_VERSION = ${{ github.event.inputs.minor }};" >> version.ts
          echo "export const PATCH_VERSION = ${{ github.event.inputs.patch }};" >> version.ts

      - name: Run update-version script
        run: node scripts/update-version.js

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v6
        with:
          commit-message: "chore: update version to ${{ github.event.inputs.major }}.${{ github.event.inputs.minor }}.${{ github.event.inputs.patch }}"
          title: "Update version to ${{ github.event.inputs.major }}.${{ github.event.inputs.minor }}.${{ github.event.inputs.patch }}"
          body: "This PR updates the version to ${{ github.event.inputs.major }}.${{ github.event.inputs.minor }}.${{ github.event.inputs.patch }}."
          branch: "update-version/${{ github.event.inputs.major }}.${{ github.event.inputs.minor }}.${{ github.event.inputs.patch }}"
          delete-branch: true
