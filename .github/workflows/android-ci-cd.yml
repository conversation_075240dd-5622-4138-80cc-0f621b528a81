name: Build Android APK and Upload to Google Drive

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Java JDK
        uses: actions/setup-java@v3
        with:
          distribution: "temurin"
          java-version: "17"

      - name: Install Node.js v20 and NPM
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Install project dependencies
        run: npm install --legacy-peer-deps

      - name: Install Expo CLI and dependencies (if Expo project)
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install EAS CLI globally
        run: npm install -g eas-cli

      - name: Prebuild Expo project for Android
        run: |
          cp .env.staging.sample .env
          npx expo prebuild --platform android --clean
        continue-on-error: true

      - name: Set up Android SDK
        uses: android-actions/setup-android@v3

      - name: Build Android APK
        working-directory: android
        run: ./gradlew clean app:assembleRelease

      - name: Get current date and time
        id: datetime
        run: echo "datetime=$(date +'%Y%m%d_%H%M%S')" >> $GITHUB_OUTPUT

      - name: Rename APK file
        run: |
          mv android/app/build/outputs/apk/release/app-release.apk android/app/build/outputs/apk/release/briky-lend-${{ steps.datetime.outputs.datetime }}.apk

      - name: Setup Google Drive credentials
        run: |
          # Clean up the JSON and remove any extra whitespace/newlines
          echo '${{ secrets.GOOGLE_DRIVE_CREDENTIALS }}' | tr -d '\n\r' | jq '.' > /tmp/google-credentials.json
          echo "Credentials file created and cleaned"
          ls -la /tmp/google-credentials.json

      - name: Install Google Drive CLI
        run: |
          pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib

      - name: Upload APK to Google Drive
        env:
          GOOGLE_APPLICATION_CREDENTIALS: /tmp/google-credentials.json
          FOLDER_ID: ${{ secrets.GOOGLE_DRIVE_FOLDER_ID }}
        run: |
          python3 << 'EOF'
          import os
          import json
          from googleapiclient.discovery import build
          from googleapiclient.http import MediaFileUpload
          from google.oauth2 import service_account

          try:
              # Debug: Check credentials file
              with open('/tmp/google-credentials.json', 'r') as f:
                  creds_data = json.load(f)
                  print(f"Credentials loaded for: {creds_data.get('client_email', 'Unknown')}")

              # Setup credentials
              credentials = service_account.Credentials.from_service_account_file(
                  '/tmp/google-credentials.json',
                  scopes=['https://www.googleapis.com/auth/drive.file']
              )

              # Build the service
              service = build('drive', 'v3', credentials=credentials)

              # File details
              file_path = 'android/app/build/outputs/apk/release/briky-lend-${{ steps.datetime.outputs.datetime }}.apk'
              file_name = 'briky-lend-${{ steps.datetime.outputs.datetime }}.apk'
              folder_id = os.environ.get('FOLDER_ID')

              print(f"Uploading {file_path} as {file_name}")
              if folder_id:
                  print(f"Target folder ID: {folder_id}")
              else:
                  print("No folder ID specified, uploading to root")

              # Upload file
              file_metadata = {
                  'name': file_name,
                  'parents': [folder_id] if folder_id else []
              }

              media = MediaFileUpload(file_path, resumable=True)
              file = service.files().create(
                  body=file_metadata,
                  media_body=media,
                  fields='id,name,webViewLink'
              ).execute()

              print(f'✅ APK uploaded successfully!')
              print(f'File ID: {file.get("id")}')
              print(f'File Name: {file.get("name")}')
              print(f'View Link: {file.get("webViewLink", "N/A")}')

          except Exception as e:
              print(f'❌ Error uploading APK: {str(e)}')
              import traceback
              traceback.print_exc()
              exit(1)
          EOF

      - name: Cleanup credentials
        if: always()
        run: rm -f /tmp/google-credentials.json
