name: ESLint Check

on:
  push:
    branches:
      - main
  pull_request_target:
    branches:
      - main

jobs:
  eslint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "22.13.1"

      - name: Install dependencies
        run: npm install

      - name: Run ESLint
        run: make check-format
